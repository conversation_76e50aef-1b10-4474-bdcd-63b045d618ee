#!/usr/bin/env python3
"""
简单检查菜单权限问题
"""

import requests
import json

def test_menu_api():
    """测试菜单API"""
    print("🔍 测试菜单API...")
    
    # 测试登录
    login_url = "http://localhost:8000/dev-api/login"
    login_data = {
        "username": "admin",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result.get('data', {}).get('access_token')
                print(f"✅ 登录成功，获取到token")
                
                # 测试获取路由
                router_url = "http://localhost:8000/dev-api/getRouters"
                headers = {"Authorization": f"Bearer {token}"}
                
                router_response = requests.get(router_url, headers=headers)
                if router_response.status_code == 200:
                    router_result = router_response.json()
                    if router_result.get('code') == 200:
                        routers = router_result.get('data', [])
                        print(f"✅ 获取路由成功，共 {len(routers)} 个路由")
                        
                        # 检查是否有项目管理路由
                        project_routes = [r for r in routers if 'project' in r.get('path', '').lower()]
                        if project_routes:
                            print("✅ 找到项目管理路由:")
                            for route in project_routes:
                                print(f"  - {route.get('name')}: {route.get('path')}")
                        else:
                            print("❌ 没有找到项目管理路由")
                    else:
                        print(f"❌ 获取路由失败: {router_result.get('msg')}")
                else:
                    print(f"❌ 获取路由请求失败: {router_response.status_code}")
            else:
                print(f"❌ 登录失败: {result.get('msg')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_user_info():
    """测试用户信息API"""
    print("\n🔍 测试用户信息API...")
    
    # 测试登录
    login_url = "http://localhost:8000/dev-api/login"
    login_data = {
        "username": "admin",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result.get('data', {}).get('access_token')
                
                # 测试获取用户信息
                info_url = "http://localhost:8000/dev-api/getInfo"
                headers = {"Authorization": f"Bearer {token}"}
                
                info_response = requests.get(info_url, headers=headers)
                if info_response.status_code == 200:
                    info_result = info_response.json()
                    if info_result.get('code') == 200:
                        user_info = info_result.get('data', {})
                        user = user_info.get('user', {})
                        roles = user_info.get('roles', [])
                        permissions = user_info.get('permissions', [])
                        
                        print(f"✅ 用户信息: {user.get('userName')} (ID: {user.get('userId')})")
                        print(f"✅ 用户角色: {[r for r in roles]}")
                        print(f"✅ 用户权限数量: {len(permissions)}")
                        
                        # 检查项目相关权限
                        project_perms = [p for p in permissions if 'project' in p.lower()]
                        if project_perms:
                            print("✅ 项目相关权限:")
                            for perm in project_perms:
                                print(f"  - {perm}")
                        else:
                            print("❌ 没有项目相关权限")
                    else:
                        print(f"❌ 获取用户信息失败: {info_result.get('msg')}")
                else:
                    print(f"❌ 获取用户信息请求失败: {info_response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始简单检查...")
    
    test_user_info()
    test_menu_api()
    
    print("\n💡 如果没有项目相关权限，请检查:")
    print("1. 数据库中sys_role表是否有project_member角色")
    print("2. sys_role_menu表是否为project_member角色分配了菜单权限")
    print("3. sys_user_role表是否为用户分配了project_member角色")

if __name__ == "__main__":
    main()
