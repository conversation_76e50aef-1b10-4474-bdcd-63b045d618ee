#!/usr/bin/env python3
"""
测试前端修复效果
"""

import requests
import json

def test_login_and_permissions():
    """测试登录和权限"""
    base_url = "http://localhost:8000/dev-api"
    
    print("🔍 测试管理员登录和权限...")
    
    # 管理员登录
    admin_response = requests.post(f"{base_url}/login", json={
        "username": "admin",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    })
    
    if admin_response.status_code == 200:
        admin_result = admin_response.json()
        if admin_result.get('code') == 200:
            admin_token = admin_result.get('data', {}).get('access_token')
            print("✅ 管理员登录成功")
            
            # 获取用户信息
            headers = {"Authorization": f"Bearer {admin_token}"}
            info_response = requests.get(f"{base_url}/getInfo", headers=headers)
            
            if info_response.status_code == 200:
                info_result = info_response.json()
                if info_result.get('code') == 200:
                    user_info = info_result.get('data', {})
                    user = user_info.get('user', {})
                    roles = user_info.get('roles', [])
                    permissions = user_info.get('permissions', [])
                    
                    print(f"✅ 用户: {user.get('userName')} (ID: {user.get('userId')})")
                    print(f"✅ 角色: {roles}")
                    print(f"✅ 权限数量: {len(permissions)}")
                    
                    # 检查创建项目权限
                    has_create_permission = (
                        'admin' in roles or 
                        'project_manager' in roles or
                        'business:projects:add' in permissions or
                        '*:*:*' in permissions
                    )
                    
                    print(f"✅ 创建项目权限: {has_create_permission}")
                    
                    if has_create_permission:
                        print("✅ 前端应该显示'创建新项目'按钮")
                    else:
                        print("❌ 前端不应该显示'创建新项目'按钮")
                    
                    return True
        
    print("❌ 测试失败")
    return False

def test_project_member_permissions():
    """测试项目成员权限"""
    base_url = "http://localhost:8000/dev-api"
    
    print("\n🔍 测试项目成员登录和权限...")
    
    # 项目成员登录
    member_response = requests.post(f"{base_url}/login", json={
        "username": "testmember",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    })
    
    if member_response.status_code == 200:
        member_result = member_response.json()
        if member_result.get('code') == 200:
            member_token = member_result.get('data', {}).get('access_token')
            print("✅ 项目成员登录成功")
            
            # 获取用户信息
            headers = {"Authorization": f"Bearer {member_token}"}
            info_response = requests.get(f"{base_url}/getInfo", headers=headers)
            
            if info_response.status_code == 200:
                info_result = info_response.json()
                if info_result.get('code') == 200:
                    user_info = info_result.get('data', {})
                    user = user_info.get('user', {})
                    roles = user_info.get('roles', [])
                    permissions = user_info.get('permissions', [])
                    
                    print(f"✅ 用户: {user.get('userName')} (ID: {user.get('userId')})")
                    print(f"✅ 角色: {roles}")
                    print(f"✅ 权限数量: {len(permissions)}")
                    
                    # 检查创建项目权限
                    has_create_permission = (
                        'admin' in roles or 
                        'project_manager' in roles or
                        'business:projects:add' in permissions or
                        '*:*:*' in permissions
                    )
                    
                    print(f"✅ 创建项目权限: {has_create_permission}")
                    
                    if not has_create_permission:
                        print("✅ 前端不应该显示'创建新项目'按钮")
                    else:
                        print("❌ 前端应该隐藏'创建新项目'按钮")
                    
                    # 测试项目列表
                    projects_response = requests.get(f"{base_url}/business/projects/list", headers=headers)
                    if projects_response.status_code == 200:
                        projects_result = projects_response.json()
                        if projects_result.get('code') == 200:
                            projects = projects_result.get('data', {}).get('rows', [])
                            print(f"✅ 项目成员可见项目数: {len(projects)}")
                            
                            for project in projects[:3]:
                                name = project.get('project_name') or project.get('projectName')
                                owner = project.get('owner_id') or project.get('ownerId')
                                print(f"  - {name} (所有者: {owner})")
                    
                    return True
        else:
            print(f"❌ 项目成员登录失败: {member_result.get('msg')}")
    else:
        print("⚠️ 项目成员用户不存在，请先执行 create_test_member.sql")
    
    return False

def main():
    """主函数"""
    print("🚀 开始测试前端修复效果...")
    
    admin_ok = test_login_and_permissions()
    member_ok = test_project_member_permissions()
    
    print(f"\n📊 测试总结:")
    print(f"{'✅' if admin_ok else '❌'} 管理员权限测试")
    print(f"{'✅' if member_ok else '❌'} 项目成员权限测试")
    
    if admin_ok and member_ok:
        print("\n🎉 前端权限控制修复成功！")
        print("\n📝 预期效果:")
        print("1. 管理员登录后可以看到'创建新项目'按钮")
        print("2. 项目成员登录后看不到'创建新项目'按钮")
        print("3. 编辑和删除按钮根据项目所有权显示")
    else:
        print("\n⚠️ 部分测试失败，请检查:")
        if not admin_ok:
            print("- 管理员权限配置")
        if not member_ok:
            print("- 项目成员用户创建和权限配置")
    
    return admin_ok and member_ok

if __name__ == "__main__":
    main()
