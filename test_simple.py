#!/usr/bin/env python3
"""
简单测试项目创建功能
"""

import sys
import os

# 添加项目路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-platform-backend'))

from module_business.entity.vo.projects_vo import ProjectsModel, ProjectMemberModel

def test_projects_model():
    """测试ProjectsModel是否能正确处理project_members字段"""
    print("🧪 测试ProjectsModel...")
    
    # 创建项目成员
    member1 = ProjectMemberModel(user_id=2, role_type='member')
    member2 = ProjectMemberModel(user_id=3, role_type='member')
    
    # 创建项目模型
    project = ProjectsModel(
        project_name="测试项目",
        description="这是一个测试项目",
        project_members=[member1, member2]
    )
    
    print(f"✅ 项目名称: {project.project_name}")
    print(f"✅ 项目描述: {project.description}")
    print(f"✅ 项目成员数量: {len(project.project_members) if project.project_members else 0}")
    
    if project.project_members:
        for i, member in enumerate(project.project_members):
            print(f"   成员{i+1}: 用户ID={member.user_id}, 角色={member.role_type}")
    
    # 测试model_dump排除字段
    project_data = project.model_dump(exclude={'project_members'})
    print(f"✅ 排除project_members后的字段: {list(project_data.keys())}")
    
    if 'project_members' not in project_data:
        print("✅ 成功排除project_members字段")
    else:
        print("❌ 未能排除project_members字段")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始简单测试...")
    
    try:
        if test_projects_model():
            print("\n🎉 测试成功！ProjectsModel可以正确处理project_members字段")
        else:
            print("\n❌ 测试失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
