import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { useUserStore, useSettingsStore, usePermissionStore } from '@/store/modules'
import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/ruoyi'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/register'];

/**
 * 路由前置守卫
 */
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const hasToken = getToken()
  const settingsStore = useSettingsStore()
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (userStore.roles.length === 0) {
        try {
          await userStore.getInfo()
          const accessRoutes = await permissionStore.generateRoutes()
          accessRoutes.forEach(route => {
            if (!isHttp(route.path)) {
              router.addRoute(route)
            }
          })
          next({ ...to, replace: true })
        } catch (error) {
          await userStore.logOut()
          ElMessage.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      } else {
        next()
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

/**
 * 检查菜单访问权限
 * @param {string} path - 菜单路径
 * @param {Array} permissions - 用户权限列表
 * @returns {boolean} - 是否有访问权限
 */
export function checkMenuAccess(path, permissions) {
  // 超级管理员拥有所有权限
  if (permissions.includes('*:*:*')) {
    return true
  }

  // 菜单权限映射
  const menuPermissionMap = {
    '/': ['menu:home'],
    '/home': ['menu:home'],
    '/tools': ['menu:tools'],
    '/projects': ['menu:project'],
    '/knowledge-bases': ['menu:database']
  }

  const requiredPermissions = menuPermissionMap[path]
  if (!requiredPermissions) {
    return true // 没有配置的路径默认允许访问
  }

  return requiredPermissions.some(permission => permissions.includes(permission))
}

/**
 * 检查权限
 * @param {string|Array} permission - 权限标识或权限列表
 * @returns {boolean} - 是否有权限
 */
export function checkPermission(permission) {
  const userStore = useUserStore()
  const permissions = userStore.permissions

  if (permissions.includes('*:*:*')) {
    return true
  }

  if (Array.isArray(permission)) {
    return permission.some(perm => permissions.includes(perm))
  }

  return permissions.includes(permission)
}

/**
 * 检查角色
 * @param {string|Array} role - 角色标识或角色列表
 * @returns {boolean} - 是否有角色
 */
export function checkRole(role) {
  const userStore = useUserStore()
  const roles = userStore.roles

  if (Array.isArray(role)) {
    return role.some(r => roles.includes(r))
  }

  return roles.includes(role)
}

/**
 * 检查项目权限
 * @param {number} projectId - 项目ID
 * @param {string} action - 操作类型 (view, edit, delete, manage_members)
 * @returns {Promise<boolean>} - 是否有权限
 */
export async function checkProjectPermission(projectId, action) {
  try {
    const response = await fetch(`/api/check-permission/${projectId}?action=${action}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      return result.code === 200 && result.data
    }
    
    return false
  } catch (error) {
    console.error('检查项目权限失败:', error)
    return false
  }
}

/**
 * 检查任务权限
 * @param {number} taskId - 任务ID
 * @param {string} action - 操作类型 (view, edit, delete, execute, download)
 * @returns {Promise<boolean>} - 是否有权限
 */
export async function checkTaskPermission(taskId, action) {
  try {
    const response = await fetch(`/api/check-task-permission/${taskId}?action=${action}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      return result.code === 200 && result.data
    }
    
    return false
  } catch (error) {
    console.error('检查任务权限失败:', error)
    return false
  }
}
