#!/usr/bin/env python3
"""
综合修复脚本 - 解决所有核心问题
"""

import requests
import json
import time

class ComprehensiveFixer:
    def __init__(self, base_url="http://localhost:8000/dev-api"):
        self.base_url = base_url
        self.admin_token = None
    
    def login_admin(self):
        """管理员登录"""
        print("🔐 管理员登录...")
        response = requests.post(f"{self.base_url}/login", json={
            "username": "admin",
            "password": "admin123",
            "code": "1234",
            "uuid": "test"
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                self.admin_token = result.get('data', {}).get('access_token')
                print("✅ 管理员登录成功")
                return True
        
        print("❌ 管理员登录失败")
        return False
    
    def check_menu_structure(self):
        """检查菜单结构"""
        print("\n🔍 检查菜单结构...")
        
        if not self.admin_token:
            print("❌ 需要先登录")
            return False
        
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        response = requests.get(f"{self.base_url}/getRouters", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                routes = result.get('data', [])
                project_routes = [r for r in routes if r.get('path') == '/projects']
                
                print(f"总路由数: {len(routes)}")
                print(f"项目管理路由数: {len(project_routes)}")
                
                if len(project_routes) == 1:
                    project_route = project_routes[0]
                    children = project_route.get('children', [])
                    print(f"✅ 项目管理路由正常，子路由数: {len(children)}")
                    
                    # 检查子路由
                    for child in children:
                        print(f"  - {child.get('name')}: {child.get('path')}")
                    
                    return True
                elif len(project_routes) == 0:
                    print("❌ 没有找到项目管理路由")
                    return False
                else:
                    print(f"❌ 发现重复的项目管理路由: {len(project_routes)} 个")
                    # 显示重复的路由
                    for i, route in enumerate(project_routes):
                        print(f"  路由{i+1}: {route.get('name')} - {route.get('path')}")
                    return False
        
        print("❌ 获取路由失败")
        return False
    
    def test_project_list_permission(self):
        """测试项目列表权限"""
        print("\n🔍 测试项目列表权限...")
        
        if not self.admin_token:
            print("❌ 需要先登录")
            return False
        
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        response = requests.get(f"{self.base_url}/business/projects/list", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                projects = result.get('data', {}).get('rows', [])
                total = result.get('data', {}).get('total', 0)
                
                print(f"✅ 项目列表获取成功")
                print(f"当前页项目数: {len(projects)}")
                print(f"总项目数: {total}")
                
                # 显示项目信息
                for i, project in enumerate(projects[:3]):
                    print(f"  项目{i+1}: {project.get('project_name')} (所有者ID: {project.get('owner_id')})")
                
                return True
            else:
                print(f"❌ 获取项目列表失败: {result.get('msg')}")
        else:
            print(f"❌ 项目列表请求失败: {response.status_code}")
        
        return False
    
    def test_project_detail_with_members(self):
        """测试项目详情包含成员信息"""
        print("\n🔍 测试项目详情包含成员信息...")
        
        if not self.admin_token:
            print("❌ 需要先登录")
            return False
        
        # 先获取项目列表
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        list_response = requests.get(f"{self.base_url}/business/projects/list", headers=headers)
        
        if list_response.status_code != 200:
            print("❌ 无法获取项目列表")
            return False
        
        list_result = list_response.json()
        if list_result.get('code') != 200:
            print("❌ 获取项目列表失败")
            return False
        
        projects = list_result.get('data', {}).get('rows', [])
        if not projects:
            print("⚠️ 没有项目可以测试")
            return True
        
        # 测试第一个项目的详情
        project = projects[0]
        project_id = project.get('project_id') or project.get('projectId')
        
        if not project_id:
            print("❌ 无法获取项目ID")
            return False
        
        print(f"测试项目: {project.get('project_name')} (ID: {project_id})")
        
        detail_response = requests.get(f"{self.base_url}/business/projects/{project_id}", headers=headers)
        
        if detail_response.status_code == 200:
            detail_result = detail_response.json()
            if detail_result.get('code') == 200:
                project_detail = detail_result.get('data', {})
                
                print("✅ 项目详情获取成功")
                print(f"项目名称: {project_detail.get('project_name') or project_detail.get('projectName')}")
                print(f"所有者ID: {project_detail.get('owner_id') or project_detail.get('ownerId')}")
                
                # 检查成员信息
                members = project_detail.get('project_members') or project_detail.get('projectMembers')
                if members is not None:
                    print(f"✅ 项目成员信息存在，成员数: {len(members)}")
                    for i, member in enumerate(members[:3]):
                        user_id = member.get('user_id') or member.get('userId')
                        role_type = member.get('role_type') or member.get('roleType')
                        print(f"  成员{i+1}: 用户ID {user_id}, 角色 {role_type}")
                    return True
                else:
                    print("❌ 项目详情不包含成员信息")
                    return False
            else:
                print(f"❌ 获取项目详情失败: {detail_result.get('msg')}")
        else:
            print(f"❌ 项目详情请求失败: {detail_response.status_code}")
        
        return False
    
    def check_user_permissions(self):
        """检查用户权限"""
        print("\n🔍 检查用户权限...")
        
        if not self.admin_token:
            print("❌ 需要先登录")
            return False
        
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        response = requests.get(f"{self.base_url}/getInfo", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                user_info = result.get('data', {})
                user = user_info.get('user', {})
                roles = user_info.get('roles', [])
                permissions = user_info.get('permissions', [])
                
                print(f"✅ 用户: {user.get('userName')} (ID: {user.get('userId')})")
                print(f"✅ 角色: {roles}")
                print(f"✅ 权限数量: {len(permissions)}")
                
                # 检查超级管理员权限
                has_all_permission = '*:*:*' in permissions
                print(f"✅ 超级管理员权限: {has_all_permission}")
                
                # 检查项目相关权限
                project_perms = [p for p in permissions if 'project' in p.lower()]
                print(f"✅ 项目相关权限: {len(project_perms)}")
                for perm in project_perms[:5]:  # 只显示前5个
                    print(f"  - {perm}")
                
                return True
            else:
                print(f"❌ 获取用户信息失败: {result.get('msg')}")
        else:
            print(f"❌ 用户信息请求失败: {response.status_code}")
        
        return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始综合测试...")
        
        # 测试项目
        tests = [
            ("管理员登录", self.login_admin),
            ("菜单结构检查", self.check_menu_structure),
            ("用户权限检查", self.check_user_permissions),
            ("项目列表权限测试", self.test_project_list_permission),
            ("项目详情成员信息测试", self.test_project_detail_with_members),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n{'='*60}")
            try:
                result = test_func()
                results.append((test_name, result))
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{status} {test_name}")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                results.append((test_name, False))
            
            time.sleep(0.5)  # 避免请求过快
        
        # 总结
        print(f"\n{'='*60}")
        print("📊 测试总结:")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        print(f"通过: {passed}/{total}")
        
        for test_name, result in results:
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")
        
        # 问题分析和建议
        print(f"\n📝 问题分析和建议:")
        
        if not results[1][1]:  # 菜单结构检查失败
            print("🔧 菜单重复问题:")
            print("  1. 执行 check_menu_duplicate.sql 清理重复菜单")
            print("  2. 重启后端服务")
            print("  3. 清理浏览器缓存并重新登录")
        
        if results[3][1] and results[2][1]:  # 项目列表正常且是管理员
            print("🔧 权限过滤说明:")
            print("  1. 当前用户是管理员，可以看到所有项目（这是正常的）")
            print("  2. 要测试权限过滤，需要创建普通项目成员用户")
            print("  3. 项目成员只能看到自己参与的项目")
        
        if not results[4][1]:  # 项目详情成员信息失败
            print("🔧 项目成员信息问题:")
            print("  1. 检查项目是否有成员分配")
            print("  2. 检查 ProjectMembersService.get_project_members 方法")
            print("  3. 检查数据库 rd_project_members 表数据")
        
        return passed == total

def main():
    """主函数"""
    fixer = ComprehensiveFixer()
    success = fixer.run_comprehensive_test()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️ 部分测试失败，请根据建议进行修复")
    
    return success

if __name__ == "__main__":
    main()
