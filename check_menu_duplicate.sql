-- 检查菜单重复问题
SELECT '=== 检查项目相关菜单 ===' as info;

-- 查看所有项目相关菜单
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
ORDER BY parent_id, order_num;

SELECT '=== 检查根级菜单 ===' as info;

-- 查看根级菜单
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE parent_id = 0
ORDER BY order_num;

-- 删除所有项目相关菜单的角色权限分配
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
);

-- 删除所有项目相关菜单
DELETE FROM sys_menu WHERE menu_name LIKE '%项目%' OR path LIKE '%project%';

-- 重新创建唯一的项目管理菜单结构
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目管理', 0, 2, 'projects', 'Layout', '', 'Projects', 1, 0, 'M', '0', '0', 'menu:project', 'project', 'admin', NOW(), '项目管理主菜单');

-- 获取主菜单ID
SET @project_menu_id = LAST_INSERT_ID();

-- 创建项目看板子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目看板', @project_menu_id, 1, 'board', 'projects/board', '', 'ProjectsBoard', 1, 0, 'C', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW(), '项目看板菜单');

-- 创建隐藏的子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目详情', @project_menu_id, 2, 'detail/:id', 'projects/detail', '', 'ProjectDetail', 1, 0, 'C', '1', '0', 'business:projects:view', 'kanban', 'admin', NOW(), '项目详情菜单');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目成员管理', @project_menu_id, 3, 'members/:id', 'projects/members', '', 'projectMembers', 1, 0, 'C', '1', '0', 'business:projects:member:manage', 'user', 'admin', NOW(), '项目成员管理菜单');

-- 重新分配权限给管理员和项目成员角色
SET @admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'admin' LIMIT 1);
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' LIMIT 1);

-- 为管理员分配所有项目菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id FROM sys_menu
WHERE (path = 'projects' OR parent_id = @project_menu_id) AND @admin_role_id IS NOT NULL;

-- 为项目成员分配基础权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @project_member_role_id, menu_id FROM sys_menu
WHERE (path = 'projects' OR path = 'board' OR path = 'detail/:id') AND @project_member_role_id IS NOT NULL;

-- 确保只有一个项目管理主菜单
SELECT '=== 清理后的项目菜单 ===' as info;
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
ORDER BY parent_id, order_num;
