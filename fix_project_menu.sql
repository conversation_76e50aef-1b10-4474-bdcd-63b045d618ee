-- 修复项目管理菜单配置
-- 确保项目管理菜单正确配置，避免重复菜单问题

-- 1. 删除可能存在的错误配置和重复菜单
DELETE FROM sys_menu WHERE (path = 'projects' OR menu_name = '项目管理') AND parent_id = 0;
DELETE FROM sys_menu WHERE menu_name = '项目看板' OR menu_name = '项目管理';

-- 2. 创建项目管理主菜单（目录类型，使用Layout组件）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目管理', 0, 2, 'projects', 'Layout', '', 'Projects', 1, 0, 'M', '0', '0', 'menu:project', 'project', 'admin', NOW(), '项目管理主菜单');

-- 3. 获取项目管理主菜单ID
SET @project_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1);

-- 4. 删除可能存在的子菜单
DELETE FROM sys_menu WHERE parent_id = @project_menu_id;

-- 5. 创建项目看板子菜单（设置为默认重定向目标）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目看板', @project_menu_id, 1, 'board', 'projects/board', '', 'ProjectsBoard', 1, 0, 'C', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW(), '项目看板菜单');

-- 6. 创建项目详情子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目详情', @project_menu_id, 2, 'detail/:id', 'projects/detail', '', 'ProjectDetail', 1, 0, 'C', '1', '0', 'business:projects:view', 'kanban', 'admin', NOW(), '项目详情菜单');

-- 7. 创建项目成员管理子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目成员管理', @project_menu_id, 3, 'members/:id', 'projects/members', '', 'projectMembers', 1, 0, 'C', '1', '0', 'business:projects:member:manage', 'user', 'admin', NOW(), '项目成员管理菜单');

-- 8. 创建创建任务子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('创建任务', @project_menu_id, 4, 'create-task/:projectId', 'projects/create-task', '', 'CreateTask', 1, 0, 'C', '1', '0', 'business:tasks:add', 'kanban', 'admin', NOW(), '创建任务菜单');

-- 7. 确保项目成员角色存在
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
SELECT '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0');

-- 8. 获取项目成员角色ID
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' LIMIT 1);

-- 9. 为项目成员角色分配菜单权限
-- 删除现有权限（重新分配）
DELETE FROM sys_role_menu WHERE role_id = @project_member_role_id;

-- 分配项目管理相关权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_member_role_id, menu_id FROM sys_menu 
WHERE perms IN (
    'menu:project',              -- 项目管理主菜单
    'business:projects:list',    -- 项目列表权限
    'business:projects:view',    -- 项目查看权限
    'menu:tools',               -- 工具菜单
    'menu:home'                 -- 首页菜单
) AND status = '0' AND @project_member_role_id IS NOT NULL;

-- 也可以通过菜单路径分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_member_role_id, menu_id FROM sys_menu 
WHERE (path = 'projects' OR path = 'board') 
AND status = '0' AND @project_member_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 10. 检查结果
SELECT '=== 项目管理菜单结构 ===' as info;
SELECT m.menu_id, m.menu_name, m.parent_id, m.path, m.component, m.perms, m.visible, m.status
FROM sys_menu m
WHERE (m.path = 'projects' AND m.parent_id = 0) 
   OR m.parent_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1)
ORDER BY m.parent_id, m.order_num;

SELECT '=== 项目成员角色权限 ===' as info;
SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.path, m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'project_member' AND r.del_flag = '0' AND m.status = '0'
ORDER BY m.parent_id, m.order_num;

-- 提交更改
COMMIT;
