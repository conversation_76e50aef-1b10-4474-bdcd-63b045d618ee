#!/usr/bin/env python3
"""
测试项目创建功能，验证项目成员是否能正确添加
"""

import json
import requests
import sys

# 测试配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/admin/login"
CREATE_PROJECT_URL = f"{BASE_URL}/business/projects"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    }
    
    try:
        response = requests.post(LOGIN_URL, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result.get('data', {}).get('access_token')
                print(f"登录成功，获取到token: {token[:20]}...")
                return token
            else:
                print(f"登录失败: {result.get('msg')}")
                return None
        else:
            print(f"登录请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"登录异常: {e}")
        return None

def create_project_with_members(token):
    """创建包含成员的项目"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试数据：创建项目并添加成员
    project_data = {
        "projectName": "测试项目_成员功能",
        "description": "这是一个测试项目成员功能的项目",
        "projectMembers": [
            {
                "userId": 2,  # 假设用户ID为2的用户存在
                "roleType": "member"
            },
            {
                "userId": 3,  # 假设用户ID为3的用户存在
                "roleType": "member"
            }
        ]
    }
    
    try:
        response = requests.post(CREATE_PROJECT_URL, json=project_data, headers=headers)
        print(f"创建项目请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"创建项目响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('code') == 200:
                print("✅ 项目创建成功！")
                project_id = result.get('data', {}).get('project_id') or result.get('data', {}).get('projectId')
                if project_id:
                    print(f"项目ID: {project_id}")
                    return project_id
                else:
                    print("⚠️ 无法获取项目ID")
                    return None
            else:
                print(f"❌ 项目创建失败: {result.get('msg')}")
                return None
        else:
            print(f"❌ 创建项目请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 创建项目异常: {e}")
        return None

def verify_project_members(token, project_id):
    """验证项目成员是否正确添加"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    members_url = f"{BASE_URL}/business/project-members/list/{project_id}"
    
    try:
        response = requests.get(members_url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                members = result.get('data', [])
                print(f"✅ 项目成员列表获取成功，共 {len(members)} 个成员:")
                for member in members:
                    print(f"  - 用户ID: {member.get('user_id')}, 用户名: {member.get('user_name')}, 角色: {member.get('role_type')}")
                return True
            else:
                print(f"❌ 获取项目成员失败: {result.get('msg')}")
                return False
        else:
            print(f"❌ 获取项目成员请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取项目成员异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试项目创建功能...")
    
    # 1. 登录
    print("\n1. 正在登录...")
    token = login()
    if not token:
        print("❌ 登录失败，测试终止")
        sys.exit(1)
    
    # 2. 创建项目
    print("\n2. 正在创建项目...")
    project_id = create_project_with_members(token)
    if not project_id:
        print("❌ 项目创建失败，测试终止")
        sys.exit(1)
    
    # 3. 验证项目成员
    print("\n3. 正在验证项目成员...")
    if verify_project_members(token, project_id):
        print("\n🎉 测试成功！项目创建和成员添加功能正常工作")
    else:
        print("\n❌ 测试失败！项目成员验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
