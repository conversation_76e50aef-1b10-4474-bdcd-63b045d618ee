-- 修复菜单权限问题 -- 确保项目成员角色能看到项目管理菜单 -- 1. 检查当前角色和菜单情况 SELECT '=== 当前角色情况 ===' as info; SELECT role_id, role_name, role_key, status, del_flag FROM sys_role WHERE del_flag = '0'; SELECT '=== 项目相关菜单情况 ===' as info; SELECT menu_id, menu_name, parent_id, path, component, perms, visible, status FROM sys_menu WHERE (menu_name LIKE '%项目%' OR path LIKE '%project%' OR perms LIKE '%project%') AND status = '0' ORDER BY parent_id, order_num; -- 2. 检查项目成员角色的菜单权限 SELECT '=== 项目成员角色当前权限 ===' as info; SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.path, m.perms FROM sys_role_menu rm JOIN sys_role r ON rm.role_id = r.role_id JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE r.role_key = 'project_member' AND r.del_flag = '0' AND m.status = '0' ORDER BY m.parent_id, m.order_num; -- 3. 获取项目成员角色ID SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' LIMIT 1); -- 4. 如果项目成员角色不存在，创建它 INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) SELECT '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色' WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0'); -- 重新获取角色ID SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' LIMIT 1); -- 5. 确保项目管理相关菜单存在 -- 项目管理主菜单 INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) SELECT '项目管理', 0, 2, 'projects', 'projects/index', '', 'Projects', 1, 0, 'C', '0', '0', 'menu:project', 'project', 'admin', NOW(), '项目管理菜单' WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = 'projects' AND parent_id = 0); -- 项目列表菜单 INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) SELECT '项目列表', (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1), 1, 'list', 'projects/list', '', 'ProjectList', 1, 0, 'C', '0', '0', 'business:projects:list', 'list', 'admin', NOW(), '项目列表菜单' WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:list'); -- 6. 为项目成员角色分配必要的菜单权限 -- 删除现有权限（重新分配） DELETE FROM sys_role_menu WHERE role_id = @project_member_role_id; -- 分配新权限 INSERT INTO sys_role_menu (role_id, menu_id) SELECT @project_member_role_id, menu_id FROM sys_menu WHERE perms IN ( 'menu:project', -- 项目管理菜单 'business:projects:list', -- 项目列表权限 'menu:tools', -- 工具菜单 'menu:home' -- 首页菜单 ) AND status = '0' AND @project_member_role_id IS NOT NULL; -- 7. 检查修复结果 SELECT '=== 修复后项目成员角色权限 ===' as info; SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.path, m.perms FROM sys_role_menu rm JOIN sys_role r ON rm.role_id = r.role_id JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE r.role_key = 'project_member' AND r.del_flag = '0' AND m.status = '0' ORDER BY m.parent_id, m.order_num; -- 8. 检查用户角色分配情况 SELECT '=== 用户角色分配情况 ===' as info; SELECT ur.user_id, u.user_name, ur.role_id, r.role_name, r.role_key FROM sys_user_role ur JOIN sys_user u ON ur.user_id = u.user_id JOIN sys_role r ON ur.role_id = r.role_id WHERE r.role_key IN ('project_member', 'project_manager', 'admin') AND u.del_flag = '0' AND r.del_flag = '0' ORDER BY ur.user_id, r.role_sort; -- 9. 提交更改 COMMIT; 