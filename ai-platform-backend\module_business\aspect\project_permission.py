"""
项目权限控制装饰器
"""
from functools import wraps
from typing import Optional
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.role_constants import RoleConstants, PermissionConstants
from utils.log_util import logger


class ProjectPermissionChecker:
    """项目权限检查器"""
    
    @staticmethod
    async def check_is_admin(db: AsyncSession, user_id: int) -> bool:
        """检查用户是否是管理员"""
        try:
            admin_check = await db.execute(text("""
                SELECT 1 FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id 
                AND r.role_key = 'admin'
                AND ur.del_flag = '0'
                AND r.status = '0'
            """), {"user_id": user_id})
            return admin_check.fetchone() is not None
        except Exception as e:
            logger.error(f"检查管理员权限失败: {e}")
            return False
    
    @staticmethod
    async def check_project_owner(db: AsyncSession, user_id: int, project_id: int) -> bool:
        """检查用户是否是项目所有者"""
        try:
            owner_check = await db.execute(text("""
                SELECT 1 FROM rd_projects 
                WHERE project_id = :project_id 
                AND owner_id = :user_id
                AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            return owner_check.fetchone() is not None
        except Exception as e:
            logger.error(f"检查项目所有者权限失败: {e}")
            return False
    
    @staticmethod
    async def check_project_member(db: AsyncSession, user_id: int, project_id: int) -> bool:
        """检查用户是否是项目成员"""
        try:
            member_check = await db.execute(text("""
                SELECT 1 FROM rd_project_members 
                WHERE project_id = :project_id 
                AND user_id = :user_id
                AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            return member_check.fetchone() is not None
        except Exception as e:
            logger.error(f"检查项目成员权限失败: {e}")
            return False
    
    @staticmethod
    async def check_project_manager(db: AsyncSession, user_id: int, project_id: int) -> bool:
        """检查用户是否是项目管理员"""
        try:
            manager_check = await db.execute(text("""
                SELECT 1 FROM rd_project_members 
                WHERE project_id = :project_id 
                AND user_id = :user_id
                AND role_type = 'manager'
                AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            return manager_check.fetchone() is not None
        except Exception as e:
            logger.error(f"检查项目管理员权限失败: {e}")
            return False
    
    @staticmethod
    async def check_task_creator(db: AsyncSession, user_id: int, task_id: int) -> bool:
        """检查用户是否是任务创建者"""
        try:
            creator_check = await db.execute(text("""
                SELECT 1 FROM rd_tasks 
                WHERE task_id = :task_id 
                AND created_by = :user_id
                AND is_deleted = 0
            """), {"task_id": task_id, "user_id": user_id})
            return creator_check.fetchone() is not None
        except Exception as e:
            logger.error(f"检查任务创建者权限失败: {e}")
            return False


class ProjectPermission:
    """项目权限装饰器"""
    
    @staticmethod
    def require_project_access(allow_member: bool = True):
        """要求项目访问权限"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从参数中提取必要信息
                db = None
                current_user = None
                project_id = None
                
                # 从kwargs中提取参数
                for key, value in kwargs.items():
                    if key == 'query_db' and hasattr(value, 'execute'):
                        db = value
                    elif key == 'current_user' and hasattr(value, 'user'):
                        current_user = value
                    elif key == 'project_id' and isinstance(value, int):
                        project_id = value
                
                if not db or not current_user or not project_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="缺少必要参数"
                    )
                
                user_id = current_user.user.user_id
                
                # 检查权限
                is_admin = await ProjectPermissionChecker.check_is_admin(db, user_id)
                if is_admin:
                    return await func(*args, **kwargs)
                
                is_owner = await ProjectPermissionChecker.check_project_owner(db, user_id, project_id)
                if is_owner:
                    return await func(*args, **kwargs)
                
                if allow_member:
                    is_member = await ProjectPermissionChecker.check_project_member(db, user_id, project_id)
                    if is_member:
                        return await func(*args, **kwargs)
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限访问此项目"
                )
            
            return wrapper
        return decorator
    
    @staticmethod
    def require_project_edit():
        """要求项目编辑权限（只有管理员和项目所有者）"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从参数中提取必要信息
                db = None
                current_user = None
                project_id = None
                
                # 从kwargs中提取参数
                for key, value in kwargs.items():
                    if key == 'query_db' and hasattr(value, 'execute'):
                        db = value
                    elif key == 'current_user' and hasattr(value, 'user'):
                        current_user = value
                    elif key == 'project_id' and isinstance(value, int):
                        project_id = value
                
                # 也可能从路径参数中获取project_id
                if not project_id:
                    for arg in args:
                        if hasattr(arg, 'path_params') and 'project_id' in arg.path_params:
                            project_id = int(arg.path_params['project_id'])
                            break
                
                if not db or not current_user or not project_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="缺少必要参数"
                    )
                
                user_id = current_user.user.user_id
                
                # 检查权限：只有管理员和项目所有者可以编辑
                is_admin = await ProjectPermissionChecker.check_is_admin(db, user_id)
                if is_admin:
                    return await func(*args, **kwargs)
                
                is_owner = await ProjectPermissionChecker.check_project_owner(db, user_id, project_id)
                if is_owner:
                    return await func(*args, **kwargs)
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限编辑此项目"
                )
            
            return wrapper
        return decorator
    
    @staticmethod
    def require_task_edit():
        """要求任务编辑权限（管理员、项目所有者、任务创建者）"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 从参数中提取必要信息
                db = None
                current_user = None
                task_id = None
                project_id = None
                
                # 从kwargs中提取参数
                for key, value in kwargs.items():
                    if key == 'query_db' and hasattr(value, 'execute'):
                        db = value
                    elif key == 'current_user' and hasattr(value, 'user'):
                        current_user = value
                    elif key == 'task_id' and isinstance(value, int):
                        task_id = value
                    elif key == 'project_id' and isinstance(value, int):
                        project_id = value
                
                if not db or not current_user or not task_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="缺少必要参数"
                    )
                
                user_id = current_user.user.user_id
                
                # 检查权限
                is_admin = await ProjectPermissionChecker.check_is_admin(db, user_id)
                if is_admin:
                    return await func(*args, **kwargs)
                
                # 如果有project_id，检查是否是项目所有者
                if project_id:
                    is_owner = await ProjectPermissionChecker.check_project_owner(db, user_id, project_id)
                    if is_owner:
                        return await func(*args, **kwargs)
                
                # 检查是否是任务创建者
                is_creator = await ProjectPermissionChecker.check_task_creator(db, user_id, task_id)
                if is_creator:
                    return await func(*args, **kwargs)
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限编辑此任务"
                )
            
            return wrapper
        return decorator
