-- 更新项目成员表的角色类型枚举值
-- 添加 'owner' 角色类型到现有的 ENUM 中

-- 1. 修改 rd_project_members 表的 role_type 字段，添加 'owner' 选项
ALTER TABLE rd_project_members 
MODIFY COLUMN role_type ENUM('owner', 'manager', 'member') NOT NULL DEFAULT 'member' 
COMMENT '角色类型：owner-所有者，manager-管理员，member-成员';

-- 2. 更新现有的项目所有者记录
-- 将项目所有者在项目成员表中的角色设置为 'owner'
UPDATE rd_project_members pm
JOIN rd_projects p ON pm.project_id = p.project_id
SET pm.role_type = 'owner'
WHERE pm.user_id = p.owner_id 
AND pm.role_type = 'manager'
AND pm.is_deleted = 0;

-- 3. 为没有在项目成员表中的项目所有者添加记录
INSERT INTO rd_project_members (project_id, user_id, role_type, created_at, updated_at)
SELECT 
    p.project_id,
    p.owner_id,
    'owner',
    p.created_at,
    NOW()
FROM rd_projects p
WHERE p.is_deleted = 0
AND NOT EXISTS (
    SELECT 1 FROM rd_project_members pm 
    WHERE pm.project_id = p.project_id 
    AND pm.user_id = p.owner_id 
    AND pm.is_deleted = 0
);

-- 4. 验证更新结果
SELECT 
    p.project_id,
    p.project_name,
    p.owner_id,
    pm.role_type,
    u.user_name as owner_name
FROM rd_projects p
LEFT JOIN rd_project_members pm ON p.project_id = pm.project_id AND p.owner_id = pm.user_id AND pm.is_deleted = 0
LEFT JOIN sys_user u ON p.owner_id = u.user_id
WHERE p.is_deleted = 0
ORDER BY p.project_id;
