#!/usr/bin/env python3
"""
测试路由修复效果
"""

import requests
import json

def test_login_and_routes():
    """测试登录和路由获取"""
    base_url = "http://localhost:8000/dev-api"
    
    # 1. 登录
    print("🔐 正在登录...")
    login_response = requests.post(f"{base_url}/login", json={
        "username": "admin",
        "password": "admin123", 
        "code": "1234",
        "uuid": "test"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录请求失败: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if login_result.get('code') != 200:
        print(f"❌ 登录失败: {login_result.get('msg')}")
        return
    
    token = login_result.get('data', {}).get('access_token')
    print(f"✅ 登录成功")
    
    # 2. 获取路由
    print("\n🛣️ 正在获取路由...")
    headers = {"Authorization": f"Bearer {token}"}
    routes_response = requests.get(f"{base_url}/getRouters", headers=headers)
    
    if routes_response.status_code != 200:
        print(f"❌ 获取路由请求失败: {routes_response.status_code}")
        return
    
    routes_result = routes_response.json()
    if routes_result.get('code') != 200:
        print(f"❌ 获取路由失败: {routes_result.get('msg')}")
        return
    
    routes = routes_result.get('data', [])
    print(f"✅ 获取路由成功，共 {len(routes)} 个路由")
    
    # 3. 检查项目管理路由
    print("\n📋 检查项目管理路由...")
    project_routes = [r for r in routes if r.get('path') == '/projects']
    
    if not project_routes:
        print("❌ 没有找到项目管理路由")
        return
    
    project_route = project_routes[0]
    print(f"✅ 找到项目管理路由:")
    print(f"   路径: {project_route.get('path')}")
    print(f"   名称: {project_route.get('name')}")
    print(f"   组件: {project_route.get('component')}")
    print(f"   重定向: {project_route.get('redirect')}")
    print(f"   子路由数量: {len(project_route.get('children', []))}")
    
    # 4. 检查子路由
    children = project_route.get('children', [])
    if children:
        print(f"\n📂 子路由详情:")
        for child in children:
            print(f"   - {child.get('name')}: {child.get('path')} -> {child.get('component')}")
    
    # 5. 验证路由结构
    print(f"\n🔍 路由结构验证:")
    
    # 检查是否有组件或子路由
    has_component = project_route.get('component') is not None
    has_children = len(children) > 0
    
    if has_component and has_children:
        print("✅ 路由结构正确：有组件且有子路由")
    elif has_component:
        print("✅ 路由结构正确：有组件")
    elif has_children:
        print("✅ 路由结构正确：有子路由")
    else:
        print("❌ 路由结构错误：既没有组件也没有子路由")
        return
    
    # 检查重定向
    redirect = project_route.get('redirect')
    if redirect and redirect != 'noRedirect':
        print(f"✅ 重定向配置正确: {redirect}")
    elif redirect == 'noRedirect':
        print("⚠️ 重定向设置为 noRedirect")
    else:
        print("⚠️ 没有重定向配置")
    
    print(f"\n🎉 路由检查完成！")

if __name__ == "__main__":
    try:
        test_login_and_routes()
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
