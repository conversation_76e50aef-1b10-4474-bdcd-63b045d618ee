#!/usr/bin/env python3
"""
测试所有修复效果的综合脚本
"""

import requests
import json
import time

class ProjectPermissionTester:
    def __init__(self, base_url="http://localhost:8000/dev-api"):
        self.base_url = base_url
        self.admin_token = None
        self.member_token = None
        
    def login(self, username, password):
        """登录获取token"""
        response = requests.post(f"{self.base_url}/login", json={
            "username": username,
            "password": password,
            "code": "1234",
            "uuid": "test"
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}).get('access_token')
        return None
    
    def get_headers(self, token):
        """获取请求头"""
        return {"Authorization": f"Bearer {token}"}
    
    def test_menu_structure(self):
        """测试菜单结构"""
        print("\n🔍 测试菜单结构...")
        
        # 管理员登录
        self.admin_token = self.login("admin", "admin123")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 获取路由
        response = requests.get(f"{self.base_url}/getRouters", 
                              headers=self.get_headers(self.admin_token))
        
        if response.status_code != 200:
            print("❌ 获取路由失败")
            return False
        
        result = response.json()
        if result.get('code') != 200:
            print(f"❌ 获取路由失败: {result.get('msg')}")
            return False
        
        routes = result.get('data', [])
        project_routes = [r for r in routes if r.get('path') == '/projects']
        
        if len(project_routes) == 0:
            print("❌ 没有找到项目管理路由")
            return False
        elif len(project_routes) > 1:
            print(f"❌ 找到重复的项目管理路由: {len(project_routes)} 个")
            return False
        
        project_route = project_routes[0]
        children = project_route.get('children', [])
        
        print(f"✅ 项目管理路由结构正确:")
        print(f"   - 主路由: {project_route.get('path')}")
        print(f"   - 组件: {project_route.get('component')}")
        print(f"   - 重定向: {project_route.get('redirect')}")
        print(f"   - 子路由数量: {len(children)}")
        
        # 检查必要的子路由
        required_children = ['board', 'detail/:id', 'members/:id']
        found_children = [child.get('path') for child in children]
        
        for required in required_children:
            if required in found_children:
                print(f"   ✅ 找到子路由: {required}")
            else:
                print(f"   ❌ 缺少子路由: {required}")
        
        return True
    
    def test_project_list_permission(self):
        """测试项目列表权限过滤"""
        print("\n🔍 测试项目列表权限过滤...")
        
        if not self.admin_token:
            print("❌ 需要先登录管理员")
            return False
        
        # 获取管理员的项目列表
        admin_response = requests.get(f"{self.base_url}/business/projects/list", 
                                    headers=self.get_headers(self.admin_token))
        
        if admin_response.status_code != 200:
            print("❌ 获取管理员项目列表失败")
            return False
        
        admin_result = admin_response.json()
        if admin_result.get('code') != 200:
            print(f"❌ 获取管理员项目列表失败: {admin_result.get('msg')}")
            return False
        
        admin_projects = admin_result.get('data', {}).get('rows', [])
        print(f"✅ 管理员可以看到 {len(admin_projects)} 个项目")
        
        # TODO: 测试项目成员的权限过滤
        # 这需要先创建一个项目成员用户并分配到项目
        
        return True
    
    def test_project_detail_with_members(self):
        """测试项目详情包含成员信息"""
        print("\n🔍 测试项目详情包含成员信息...")
        
        if not self.admin_token:
            print("❌ 需要先登录管理员")
            return False
        
        # 先获取项目列表
        list_response = requests.get(f"{self.base_url}/business/projects/list", 
                                   headers=self.get_headers(self.admin_token))
        
        if list_response.status_code != 200:
            print("❌ 获取项目列表失败")
            return False
        
        list_result = list_response.json()
        projects = list_result.get('data', {}).get('rows', [])
        
        if not projects:
            print("⚠️ 没有项目可以测试")
            return True
        
        # 测试第一个项目的详情
        project_id = projects[0].get('project_id') or projects[0].get('projectId')
        if not project_id:
            print("❌ 无法获取项目ID")
            return False
        
        detail_response = requests.get(f"{self.base_url}/business/projects/{project_id}", 
                                     headers=self.get_headers(self.admin_token))
        
        if detail_response.status_code != 200:
            print("❌ 获取项目详情失败")
            return False
        
        detail_result = detail_response.json()
        if detail_result.get('code') != 200:
            print(f"❌ 获取项目详情失败: {detail_result.get('msg')}")
            return False
        
        project_detail = detail_result.get('data', {})
        project_members = project_detail.get('project_members') or project_detail.get('projectMembers')
        
        if project_members is not None:
            print(f"✅ 项目详情包含成员信息，成员数量: {len(project_members)}")
            for member in project_members[:3]:  # 只显示前3个成员
                print(f"   - 用户ID: {member.get('user_id')}, 角色: {member.get('role_type')}")
        else:
            print("⚠️ 项目详情不包含成员信息")
        
        return True
    
    def test_route_navigation(self):
        """测试路由导航"""
        print("\n🔍 测试路由导航...")
        
        if not self.admin_token:
            print("❌ 需要先登录管理员")
            return False
        
        # 测试项目看板路由
        try:
            board_response = requests.get(f"{self.base_url}/business/projects/list", 
                                        headers=self.get_headers(self.admin_token))
            if board_response.status_code == 200:
                print("✅ 项目看板路由可访问")
            else:
                print(f"❌ 项目看板路由访问失败: {board_response.status_code}")
        except Exception as e:
            print(f"❌ 项目看板路由测试异常: {e}")
        
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始综合测试...")
        
        tests = [
            ("菜单结构测试", self.test_menu_structure),
            ("项目列表权限测试", self.test_project_list_permission),
            ("项目详情成员信息测试", self.test_project_detail_with_members),
            ("路由导航测试", self.test_route_navigation),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                results.append((test_name, False))
            
            time.sleep(1)  # 避免请求过快
        
        # 总结
        print(f"\n📊 测试总结:")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        print(f"通过: {passed}/{total}")
        
        for test_name, result in results:
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")
        
        return passed == total


def main():
    """主函数"""
    tester = ProjectPermissionTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️ 部分测试失败，请检查修复效果")
    
    return success


if __name__ == "__main__":
    main()
