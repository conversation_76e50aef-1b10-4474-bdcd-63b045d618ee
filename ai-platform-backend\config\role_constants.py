"""
角色和权限常量定义
"""

class RoleConstants:
    """角色常量"""
    # 系统角色
    ADMIN = 'admin'
    PROJECT_MANAGER = 'project_manager'
    PROJECT_MEMBER = 'project_member'
    NORMAL_USER = 'normal_user'
    
    # 角色列表
    ALL_ROLES = [ADMIN, PROJECT_MANAGER, PROJECT_MEMBER, NORMAL_USER]


class PermissionConstants:
    """权限常量"""
    # 超级权限
    ALL_PERMISSION = '*:*:*'
    
    # 菜单权限
    MENU_HOME = 'menu:home'
    MENU_TOOLS = 'menu:tools'
    MENU_PROJECT = 'menu:project'
    MENU_DATABASE = 'menu:database'
    
    # 项目权限
    PROJECT_CREATE = 'business:project:create'
    PROJECT_EDIT = 'business:project:edit'
    PROJECT_DELETE = 'business:project:delete'
    PROJECT_VIEW = 'business:project:view'
    PROJECT_LIST = 'business:project:list'
    
    # 项目成员权限
    PROJECT_MEMBER_MANAGE = 'business:project:member:manage'
    PROJECT_MEMBER_ADD = 'business:project:member:add'
    PROJECT_MEMBER_REMOVE = 'business:project:member:remove'
    PROJECT_MEMBER_EDIT = 'business:project:member:edit'
    
    # 任务权限
    TASK_CREATE = 'business:task:create'
    TASK_EDIT = 'business:task:edit'
    TASK_DELETE = 'business:task:delete'
    TASK_VIEW = 'business:task:view'
    TASK_LIST = 'business:task:list'
    TASK_EDIT_ALL = 'business:task:edit:all'
    TASK_DELETE_ALL = 'business:task:delete:all'
    
    # 数据库权限
    DATABASE_CREATE = 'business:database:create'
    DATABASE_EDIT = 'business:database:edit'
    DATABASE_DELETE = 'business:database:delete'
    DATABASE_VIEW = 'business:database:view'
    DATABASE_LIST = 'business:database:list'
    
    # 工具权限
    TOOL_USE = 'business:tool:use'
    TOOL_VIEW = 'business:tool:view'
    TOOL_LIST = 'business:tool:list'
    
    # 系统管理权限
    SYSTEM_USER_MANAGE = 'system:user:manage'
    SYSTEM_ROLE_MANAGE = 'system:role:manage'
    SYSTEM_MENU_MANAGE = 'system:menu:manage'
    SYSTEM_DEPT_MANAGE = 'system:dept:manage'


# 角色权限映射
ROLE_PERMISSIONS = {
    RoleConstants.ADMIN: [
        PermissionConstants.ALL_PERMISSION,
    ],
    
    RoleConstants.PROJECT_MANAGER: [
        # 菜单权限
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        PermissionConstants.MENU_PROJECT,
        PermissionConstants.MENU_DATABASE,
        
        # 项目权限
        PermissionConstants.PROJECT_CREATE,
        PermissionConstants.PROJECT_EDIT,
        PermissionConstants.PROJECT_DELETE,
        PermissionConstants.PROJECT_VIEW,
        PermissionConstants.PROJECT_LIST,
        
        # 项目成员权限
        PermissionConstants.PROJECT_MEMBER_MANAGE,
        PermissionConstants.PROJECT_MEMBER_ADD,
        PermissionConstants.PROJECT_MEMBER_REMOVE,
        PermissionConstants.PROJECT_MEMBER_EDIT,
        
        # 任务权限
        PermissionConstants.TASK_CREATE,
        PermissionConstants.TASK_EDIT,
        PermissionConstants.TASK_DELETE,
        PermissionConstants.TASK_VIEW,
        PermissionConstants.TASK_LIST,
        PermissionConstants.TASK_EDIT_ALL,
        PermissionConstants.TASK_DELETE_ALL,
        
        # 数据库权限
        PermissionConstants.DATABASE_CREATE,
        PermissionConstants.DATABASE_EDIT,
        PermissionConstants.DATABASE_DELETE,
        PermissionConstants.DATABASE_VIEW,
        PermissionConstants.DATABASE_LIST,
        
        # 工具权限
        PermissionConstants.TOOL_USE,
        PermissionConstants.TOOL_VIEW,
        PermissionConstants.TOOL_LIST,
    ],
    
    RoleConstants.PROJECT_MEMBER: [
        # 菜单权限
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        PermissionConstants.MENU_PROJECT,
        
        # 项目权限（只读）
        PermissionConstants.PROJECT_VIEW,
        PermissionConstants.PROJECT_LIST,
        
        # 任务权限
        PermissionConstants.TASK_CREATE,
        PermissionConstants.TASK_EDIT,  # 只能编辑自己的任务
        PermissionConstants.TASK_DELETE,  # 只能删除自己的任务
        PermissionConstants.TASK_VIEW,
        PermissionConstants.TASK_LIST,
        
        # 工具权限
        PermissionConstants.TOOL_USE,
        PermissionConstants.TOOL_VIEW,
        PermissionConstants.TOOL_LIST,
    ],
    
    RoleConstants.NORMAL_USER: [
        # 菜单权限
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        
        # 工具权限
        PermissionConstants.TOOL_USE,
        PermissionConstants.TOOL_VIEW,
        PermissionConstants.TOOL_LIST,
    ],
}
