from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import List, Dict, Any
from utils.log_util import logger


class ProjectMembersService:
    """
    项目成员管理服务层
    """

    @classmethod
    async def get_project_members(cls, db: AsyncSession, project_id: int, current_user_id: int) -> List[Dict[str, Any]]:
        """
        获取项目成员列表
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param current_user_id: 当前用户ID
        :return: 成员列表
        """
        try:
            logger.info(f"开始获取项目成员，项目ID: {project_id}, 当前用户ID: {current_user_id}")
            
            # 临时注释权限检查，用于调试
            # has_permission = await cls._check_project_permission(db, project_id, current_user_id)
            # if not has_permission:
            #     logger.error(f"用户 {current_user_id} 无权限访问项目 {project_id}")
            #     raise Exception("无权限访问此项目")
            logger.info(f"临时跳过权限检查，直接获取项目 {project_id} 的成员列表")
            
            # 获取项目成员列表
            logger.info(f"执行SQL查询获取项目 {project_id} 的成员列表")
            result = await db.execute(text("""
                SELECT
                    pm.id,
                    pm.user_id,
                    pm.role_type,
                    pm.created_at,
                    u.user_name,
                    u.nick_name,
                    u.email,
                    u.avatar,
                    CASE WHEN p.owner_id = pm.user_id THEN 1 ELSE 0 END as is_owner
                FROM rd_project_members pm
                JOIN sys_user u ON pm.user_id = u.user_id
                JOIN rd_projects p ON pm.project_id = p.project_id
                WHERE pm.project_id = :project_id
                AND pm.is_deleted = 0
                AND u.status = '0'
                AND u.del_flag = '0'
                ORDER BY pm.created_at DESC
            """), {"project_id": project_id})
            
            members = result.mappings().all()
            member_list = [dict(member) for member in members]

            # 详细日志记录每个成员信息
            logger.info(f"成功获取项目成员列表，成员数量: {len(member_list)}")
            for i, member in enumerate(member_list):
                logger.info(f"成员 {i+1}: 用户ID={member['user_id']}, 用户名={member['user_name']}, 角色={member['role_type']}, 是否所有者={member['is_owner']}")

            return member_list
            
        except Exception as e:
            logger.error(f"获取项目成员失败: {str(e)}")
            raise e

    @classmethod
    async def get_available_users(cls, db: AsyncSession, project_id: int, current_user_id: int) -> List[Dict[str, Any]]:
        """
        获取可添加的用户列表
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param current_user_id: 当前用户ID
        :return: 可添加的用户列表
        """
        try:
            logger.info(f"开始获取可添加用户，项目ID: {project_id}, 当前用户ID: {current_user_id}")
            
            # 首先检查项目是否存在
            project_result = await db.execute(text("""
                SELECT project_id, owner_id, is_deleted FROM rd_projects 
                WHERE project_id = :project_id
            """), {"project_id": project_id})
            project = project_result.mappings().first()
            
            if not project:
                logger.error(f"项目 {project_id} 不存在")
                raise Exception("项目不存在")
            
            if project['is_deleted'] == 1:
                logger.error(f"项目 {project_id} 已被删除")
                raise Exception("项目已被删除")
            
            logger.info(f"项目信息: {dict(project)}")
            
            # 检查用户是否有权限管理此项目
            has_permission = await cls._check_project_management_permission(db, project_id, current_user_id)
            logger.info(f"权限检查结果: {has_permission}")
            
            if not has_permission:
                logger.warning(f"用户 {current_user_id} 无权限管理项目 {project_id}")
                raise Exception("无权限管理此项目")
            
            # 获取所有可用用户，排除已经是项目成员的用户
            result = await db.execute(text("""
                SELECT 
                    u.user_id,
                    u.user_name,
                    u.nick_name,
                    u.email,
                    u.avatar
                FROM sys_user u
                WHERE u.status = '0' 
                AND u.del_flag = '0'
                AND u.user_id NOT IN (
                    SELECT pm.user_id 
                    FROM rd_project_members pm 
                    WHERE pm.project_id = :project_id 
                    AND pm.is_deleted = 0
                )
                ORDER BY u.user_name
            """), {"project_id": project_id})
            
            users = result.mappings().all()
            user_list = [dict(user) for user in users]
            logger.info(f"成功获取可添加用户列表，用户数量: {len(user_list)}")
            return user_list
            
        except Exception as e:
            logger.error(f"获取可添加用户失败: {str(e)}")
            raise e

    @classmethod
    async def add_project_member(cls, db: AsyncSession, project_id: int, user_id: int, role_type: str, current_user_id: int) -> tuple[bool, str]:
        """
        添加项目成员
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param user_id: 要添加的用户ID
        :param role_type: 角色类型
        :param current_user_id: 当前用户ID
        :return: (成功标志, 消息)
        """
        try:
            # 检查用户是否有权限管理此项目
            has_permission = await cls._check_project_management_permission(db, project_id, current_user_id)
            if not has_permission:
                return False, "无权限管理此项目"
            
            # 检查要添加的用户是否存在
            user_result = await db.execute(text("""
                SELECT user_id, user_name FROM sys_user 
                WHERE user_id = :user_id AND status = '0' AND del_flag = '0'
            """), {"user_id": user_id})
            user = user_result.mappings().first()
            if not user:
                return False, "用户不存在或已被禁用"
            
            # 检查用户是否已经是项目成员
            existing_result = await db.execute(text("""
                SELECT id FROM rd_project_members 
                WHERE project_id = :project_id AND user_id = :user_id AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            existing = existing_result.mappings().first()
            if existing:
                return False, f"用户 {user['user_name']} 已经是项目成员"
            
            # 添加项目成员
            await db.execute(text("""
                INSERT INTO rd_project_members (project_id, user_id, role_type, created_at, updated_at)
                VALUES (:project_id, :user_id, :role_type, NOW(), NOW())
            """), {
                "project_id": project_id,
                "user_id": user_id,
                "role_type": role_type
            })

            # 不在这里提交事务，让调用方控制事务
            return True, f"成功添加用户 {user['user_name']} 为项目成员"

        except Exception as e:
            # 不在这里回滚，让调用方处理
            logger.error(f"添加项目成员失败: {str(e)}")
            return False, f"添加失败: {str(e)}"

    @classmethod
    async def update_member_role(cls, db: AsyncSession, project_id: int, user_id: int, role_type: str, current_user_id: int) -> tuple[bool, str]:
        """
        更新成员角色
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param user_id: 用户ID
        :param role_type: 新角色类型
        :param current_user_id: 当前用户ID
        :return: (成功标志, 消息)
        """
        try:
            # 检查用户是否有权限管理此项目
            has_permission = await cls._check_project_management_permission(db, project_id, current_user_id)
            if not has_permission:
                return False, "无权限管理此项目"
            
            # 检查是否是项目所有者
            project_result = await db.execute(text("""
                SELECT owner_id FROM rd_projects WHERE project_id = :project_id
            """), {"project_id": project_id})
            project = project_result.mappings().first()
            if project and project['owner_id'] == user_id:
                return False, "不能修改项目所有者的角色"
            
            # 更新成员角色
            result = await db.execute(text("""
                UPDATE rd_project_members 
                SET role_type = :role_type, updated_at = NOW()
                WHERE project_id = :project_id AND user_id = :user_id AND is_deleted = 0
            """), {
                "project_id": project_id,
                "user_id": user_id,
                "role_type": role_type
            })
            
            if result.rowcount == 0:
                return False, "用户不是项目成员"

            # 不在这里提交事务，让调用方控制事务
            return True, f"成功更新成员角色为 {role_type}"

        except Exception as e:
            # 不在这里回滚，让调用方处理
            logger.error(f"更新成员角色失败: {str(e)}")
            return False, f"更新失败: {str(e)}"

    @classmethod
    async def remove_project_member(cls, db: AsyncSession, project_id: int, user_id: int, current_user_id: int) -> tuple[bool, str]:
        """
        移除项目成员
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param user_id: 要移除的用户ID
        :param current_user_id: 当前用户ID
        :return: (成功标志, 消息)
        """
        try:
            # 检查用户是否有权限管理此项目
            has_permission = await cls._check_project_management_permission(db, project_id, current_user_id)
            if not has_permission:
                return False, "无权限管理此项目"
            
            # 检查是否是项目所有者
            project_result = await db.execute(text("""
                SELECT owner_id FROM rd_projects WHERE project_id = :project_id
            """), {"project_id": project_id})
            project = project_result.mappings().first()
            if project and project['owner_id'] == user_id:
                return False, "不能移除项目所有者"
            
            # 软删除项目成员
            result = await db.execute(text("""
                UPDATE rd_project_members 
                SET is_deleted = 1, updated_at = NOW()
                WHERE project_id = :project_id AND user_id = :user_id AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            
            if result.rowcount == 0:
                return False, "用户不是项目成员"

            # 不在这里提交事务，让调用方控制事务
            return True, "成功移除项目成员"

        except Exception as e:
            # 不在这里回滚，让调用方处理
            logger.error(f"移除项目成员失败: {str(e)}")
            return False, f"移除失败: {str(e)}"

    @classmethod
    async def _check_project_permission(cls, db: AsyncSession, project_id: int, user_id: int) -> bool:
        """
        检查用户是否有权限访问项目
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param user_id: 用户ID
        :return: 是否有权限
        """
        try:
            logger.info(f"检查项目访问权限，项目ID: {project_id}, 用户ID: {user_id}")
            
            # 首先检查项目是否存在
            project_result = await db.execute(text("""
                SELECT project_id, owner_id, is_deleted FROM rd_projects 
                WHERE project_id = :project_id
            """), {"project_id": project_id})
            project = project_result.mappings().first()
            
            if not project:
                logger.error(f"项目 {project_id} 不存在")
                return False
            
            if project['is_deleted'] == 1:
                logger.error(f"项目 {project_id} 已被删除")
                return False
            
            logger.info(f"项目信息: {dict(project)}")
            
            # 检查用户是否是项目所有者
            if project['owner_id'] == user_id:
                logger.info(f"用户 {user_id} 是项目 {project_id} 的所有者")
                return True
            
            # 检查用户是否是项目成员
            member_result = await db.execute(text("""
                SELECT 1 FROM rd_project_members 
                WHERE project_id = :project_id 
                AND user_id = :user_id 
                AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            member = member_result.mappings().first()
            
            if member:
                logger.info(f"用户 {user_id} 是项目 {project_id} 的成员")
                return True
            
            # 检查用户是否是系统管理员（可选）
            admin_result = await db.execute(text("""
                SELECT 1 FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id
                AND r.role_key = 'admin'
                AND r.del_flag = '0'
                AND r.status = '0'
            """), {"user_id": user_id})
            admin = admin_result.mappings().first()
            
            if admin:
                logger.info(f"用户 {user_id} 是系统管理员，允许访问所有项目")
                return True
            
            logger.warning(f"用户 {user_id} 没有项目 {project_id} 的访问权限")
            return False
            
        except Exception as e:
            logger.error(f"检查项目权限失败: {str(e)}")
            return False

    @classmethod
    async def _check_project_management_permission(cls, db: AsyncSession, project_id: int, user_id: int) -> bool:
        """
        检查用户是否有权限管理项目
        
        :param db: 数据库会话
        :param project_id: 项目ID
        :param user_id: 用户ID
        :return: 是否有管理权限
        """
        try:
            logger.info(f"检查项目管理权限，项目ID: {project_id}, 用户ID: {user_id}")
            
            # 首先检查用户是否是项目所有者
            owner_result = await db.execute(text("""
                SELECT owner_id FROM rd_projects 
                WHERE project_id = :project_id AND is_deleted = 0
            """), {"project_id": project_id})
            owner = owner_result.mappings().first()
            
            if owner and owner['owner_id'] == user_id:
                logger.info(f"用户 {user_id} 是项目 {project_id} 的所有者")
                return True
            
            # 检查用户是否是项目管理员
            manager_result = await db.execute(text("""
                SELECT 1 FROM rd_project_members 
                WHERE project_id = :project_id 
                AND user_id = :user_id 
                AND role_type = 'manager'
                AND is_deleted = 0
            """), {"project_id": project_id, "user_id": user_id})
            manager = manager_result.mappings().first()
            
            if manager:
                logger.info(f"用户 {user_id} 是项目 {project_id} 的管理员")
                return True
            
            # 检查用户是否是系统管理员（可选）
            admin_result = await db.execute(text("""
                SELECT 1 FROM sys_user_role ur
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE ur.user_id = :user_id
                AND r.role_key = 'admin'
                AND r.del_flag = '0'
                AND r.status = '0'
            """), {"user_id": user_id})
            admin = admin_result.mappings().first()
            
            if admin:
                logger.info(f"用户 {user_id} 是系统管理员，允许管理所有项目")
                return True
            
            logger.info(f"用户 {user_id} 没有项目 {project_id} 的管理权限")
            return False
            
        except Exception as e:
            logger.error(f"检查项目管理权限失败: {str(e)}")
            return False