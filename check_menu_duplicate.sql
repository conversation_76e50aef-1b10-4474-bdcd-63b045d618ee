-- 检查菜单重复问题
SELECT '=== 检查项目相关菜单 ===' as info;

-- 查看所有项目相关菜单
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
ORDER BY parent_id, order_num;

SELECT '=== 检查根级菜单 ===' as info;

-- 查看根级菜单
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE parent_id = 0
ORDER BY order_num;

-- 删除所有项目相关菜单的角色权限分配
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
);

-- 删除所有项目相关菜单
DELETE FROM sys_menu WHERE menu_name LIKE '%项目%' OR path LIKE '%project%';

-- 重新创建简化的项目管理菜单结构（直接指向项目列表页面）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目管理', 0, 2, 'projects', 'projects/board', '', 'Projects', 1, 0, 'C', '0', '0', 'business:projects:list', 'project', 'admin', NOW(), '项目管理菜单');

-- 获取项目管理菜单ID
SET @project_menu_id = LAST_INSERT_ID();

-- 创建隐藏的子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目详情', @project_menu_id, 2, 'detail/:id', 'projects/detail', '', 'ProjectDetail', 1, 0, 'C', '1', '0', 'business:projects:view', 'kanban', 'admin', NOW(), '项目详情菜单');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目成员管理', @project_menu_id, 3, 'members/:id', 'projects/members', '', 'projectMembers', 1, 0, 'C', '1', '0', 'business:projects:member:manage', 'user', 'admin', NOW(), '项目成员管理菜单');

-- 确保项目管理员角色存在
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark)
SELECT '项目管理员', 'project_manager', 3, '2', 1, 1, '0', '0', 'admin', NOW(), '项目管理员角色'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_manager' AND del_flag = '0');

-- 确保项目成员角色存在
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark)
SELECT '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0');

-- 获取角色ID
SET @admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'admin' LIMIT 1);
SET @project_manager_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_manager' LIMIT 1);
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' LIMIT 1);

-- 创建项目相关权限菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '项目新增', @project_menu_id, 10, '', '', '', '', 1, 0, 'F', '1', '0', 'business:projects:add', '', 'admin', NOW(), '项目新增权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:add');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '项目编辑', @project_menu_id, 11, '', '', '', '', 1, 0, 'F', '1', '0', 'business:projects:edit', '', 'admin', NOW(), '项目编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:edit');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '项目删除', @project_menu_id, 12, '', '', '', '', 1, 0, 'F', '1', '0', 'business:projects:remove', '', 'admin', NOW(), '项目删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:remove');

-- 为管理员分配所有项目权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id FROM sys_menu
WHERE (path = 'projects' OR parent_id = @project_menu_id) AND @admin_role_id IS NOT NULL;

-- 为项目管理员分配项目管理权限（包括创建、编辑、删除）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @project_manager_role_id, menu_id FROM sys_menu
WHERE (path = 'projects' OR parent_id = @project_menu_id) AND @project_manager_role_id IS NOT NULL;

-- 为项目成员分配基础权限（只能查看）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @project_member_role_id, menu_id FROM sys_menu
WHERE (path = 'projects' OR perms = 'business:projects:list' OR perms = 'business:projects:view')
AND @project_member_role_id IS NOT NULL;

-- 确保只有一个项目管理主菜单
SELECT '=== 清理后的项目菜单 ===' as info;
SELECT menu_id, menu_name, parent_id, path, component, route_name, visible, status, order_num
FROM sys_menu 
WHERE menu_name LIKE '%项目%' OR path LIKE '%project%'
ORDER BY parent_id, order_num;
