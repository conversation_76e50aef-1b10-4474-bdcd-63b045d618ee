-- 创建测试项目成员用户
-- 用于测试权限过滤功能

-- 1. 创建测试用户
INSERT INTO sys_user (user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, remark)
VALUES ('testmember', '测试成员', '<EMAIL>', '13800000001', '0', '', '$2a$10$7JB720yubVSOfvVWbGReyO.Ey8Ey8Ey8Ey8Ey8Ey8Ey8Ey8Ey8Ey8Ey8', '0', '0', '', NOW(), 'admin', NOW(), '测试项目成员用户')
ON DUPLICATE KEY UPDATE user_name = VALUES(user_name);

-- 2. 获取测试用户ID
SET @test_user_id = (SELECT user_id FROM sys_user WHERE user_name = 'testmember' LIMIT 1);

-- 3. 确保项目成员角色存在
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
SELECT '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0');

-- 4. 获取项目成员角色ID
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' LIMIT 1);

-- 5. 为测试用户分配项目成员角色
INSERT INTO sys_user_role (user_id, role_id) 
VALUES (@test_user_id, @project_member_role_id)
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 6. 创建一个测试项目（如果不存在）
INSERT INTO rd_projects (project_name, description, owner_id, is_deleted, created_at, updated_at)
SELECT '测试项目A', '用于测试权限过滤的项目A', 1, 0, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM rd_projects WHERE project_name = '测试项目A');

INSERT INTO rd_projects (project_name, description, owner_id, is_deleted, created_at, updated_at)
SELECT '测试项目B', '用于测试权限过滤的项目B', 1, 0, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM rd_projects WHERE project_name = '测试项目B');

-- 7. 获取测试项目ID
SET @test_project_a_id = (SELECT project_id FROM rd_projects WHERE project_name = '测试项目A' LIMIT 1);
SET @test_project_b_id = (SELECT project_id FROM rd_projects WHERE project_name = '测试项目B' LIMIT 1);

-- 8. 将测试用户添加到项目A（但不添加到项目B）
INSERT INTO rd_project_members (project_id, user_id, role_type, is_deleted, created_at, updated_at)
VALUES (@test_project_a_id, @test_user_id, 'member', 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE role_type = VALUES(role_type), is_deleted = 0;

-- 9. 检查创建结果
SELECT '=== 测试用户信息 ===' as info;
SELECT user_id, user_name, nick_name, status FROM sys_user WHERE user_name = 'testmember';

SELECT '=== 测试用户角色 ===' as info;
SELECT u.user_name, r.role_name, r.role_key 
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_name = 'testmember';

SELECT '=== 测试项目 ===' as info;
SELECT project_id, project_name, owner_id FROM rd_projects WHERE project_name LIKE '测试项目%';

SELECT '=== 项目成员分配 ===' as info;
SELECT pm.project_id, p.project_name, pm.user_id, u.user_name, pm.role_type
FROM rd_project_members pm
JOIN rd_projects p ON pm.project_id = p.project_id
JOIN sys_user u ON pm.user_id = u.user_id
WHERE pm.is_deleted = 0 AND u.user_name = 'testmember';

-- 提交更改
COMMIT;

-- 使用说明
SELECT '=== 使用说明 ===' as info;
SELECT '1. 测试用户: testmember / admin123' as step1;
SELECT '2. 该用户只能看到"测试项目A"，看不到"测试项目B"' as step2;
SELECT '3. 管理员用户可以看到所有项目' as step3;
SELECT '4. 用测试用户登录验证权限过滤效果' as step4;
