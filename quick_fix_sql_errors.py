#!/usr/bin/env python3
"""
快速修复SQL错误的脚本
主要修复 sys_user_role 表中不存在 del_flag 字段的问题
"""

import os
import re

def fix_sql_files():
    """修复SQL文件中的错误"""
    files_to_fix = [
        'fix_all_issues.sql',
        'fix_menu_permissions.sql',
        'check_menu_permissions.py'
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"修复文件: {file_path}")
            fix_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

def fix_file(file_path):
    """修复单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复 ur.del_flag 错误
        # sys_user_role 表没有 del_flag 字段，需要移除这个条件
        patterns = [
            # 修复 AND ur.del_flag = '0'
            (r"AND ur\.del_flag = '0'", ""),
            (r"AND ur\.del_flag = \"0\"", ""),
            # 修复多余的空行和AND
            (r"\s+AND\s+r\.", " AND r."),
            # 修复多个连续的空格
            (r"\s+", " "),
        ]
        
        original_content = content
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复 {file_path}")
        else:
            print(f"⚠️ {file_path} 无需修复")
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 失败: {e}")

def fix_python_files():
    """修复Python文件中的SQL错误"""
    python_files = [
        'ai-platform-backend/module_business/dao/projects_dao.py',
        'ai-platform-backend/module_business/service/project_members_service.py',
        'ai-platform-backend/module_business/aspect/project_permission.py'
    ]
    
    for file_path in python_files:
        if os.path.exists(file_path):
            print(f"修复Python文件: {file_path}")
            fix_python_file(file_path)
        else:
            print(f"Python文件不存在: {file_path}")

def fix_python_file(file_path):
    """修复Python文件中的SQL"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复SQL中的 ur.del_flag
        patterns = [
            # 修复多行SQL中的 ur.del_flag
            (r"AND ur\.del_flag = '0'\s*\n\s*AND r\.", "AND r."),
            (r"AND ur\.del_flag = \"0\"\s*\n\s*AND r\.", "AND r."),
            # 修复单行SQL中的 ur.del_flag
            (r"AND ur\.del_flag = '0'\s+AND r\.", " AND r."),
            (r"AND ur\.del_flag = \"0\"\s+AND r\.", " AND r."),
        ]
        
        original_content = content
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复 {file_path}")
        else:
            print(f"⚠️ {file_path} 无需修复")
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 失败: {e}")

def main():
    """主函数"""
    print("🔧 开始修复SQL错误...")
    
    print("\n1. 修复SQL文件...")
    fix_sql_files()
    
    print("\n2. 修复Python文件...")
    fix_python_files()
    
    print("\n✅ 修复完成！")
    print("\n📝 修复说明:")
    print("- 移除了 sys_user_role 表查询中的 ur.del_flag 条件")
    print("- sys_user_role 表是关联表，没有删除标记字段")
    print("- 删除标记检查已移至 sys_role 表的 r.del_flag 字段")

if __name__ == "__main__":
    main()
