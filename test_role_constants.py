#!/usr/bin/env python3
"""
测试角色常量是否正确导入
"""

import sys
import os

# 添加项目路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-platform-backend'))

def test_role_constants():
    """测试角色常量导入"""
    print("🧪 测试角色常量导入...")
    
    try:
        from config.role_constants import RoleConstants, PermissionConstants, ROLE_PERMISSIONS
        
        print(f"✅ RoleConstants.PROJECT_MEMBER: {RoleConstants.PROJECT_MEMBER}")
        print(f"✅ PermissionConstants.ALL_PERMISSION: {PermissionConstants.ALL_PERMISSION}")
        print(f"✅ ROLE_PERMISSIONS keys: {list(ROLE_PERMISSIONS.keys())}")
        
        # 测试项目成员角色的权限
        project_member_permissions = ROLE_PERMISSIONS.get(RoleConstants.PROJECT_MEMBER, [])
        print(f"✅ 项目成员权限数量: {len(project_member_permissions)}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_projects_model():
    """测试项目模型"""
    print("\n🧪 测试项目模型...")
    
    try:
        from module_business.entity.vo.projects_vo import ProjectsModel, ProjectMemberModel
        
        # 创建项目成员
        member1 = ProjectMemberModel(user_id=2, role_type='member')
        member2 = ProjectMemberModel(user_id=3, role_type='member')
        
        # 创建项目模型
        project = ProjectsModel(
            project_name="测试项目",
            description="这是一个测试项目",
            project_members=[member1, member2]
        )
        
        print(f"✅ 项目名称: {project.project_name}")
        print(f"✅ 项目成员数量: {len(project.project_members) if project.project_members else 0}")
        
        # 测试model_dump排除字段
        project_data = project.model_dump(exclude={'project_members'})
        print(f"✅ 排除project_members后的字段: {list(project_data.keys())}")
        
        if 'project_members' not in project_data:
            print("✅ 成功排除project_members字段")
            return True
        else:
            print("❌ 未能排除project_members字段")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试...")
    
    success = True
    
    # 测试角色常量
    if not test_role_constants():
        success = False
    
    # 测试项目模型
    if not test_projects_model():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
