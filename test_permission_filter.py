#!/usr/bin/env python3
"""
测试权限过滤功能
"""

import requests
import json

def test_admin_vs_member():
    """测试管理员和普通成员的项目可见性差异"""
    base_url = "http://localhost:8000/dev-api"
    
    # 1. 管理员登录
    print("🔍 测试管理员权限...")
    admin_response = requests.post(f"{base_url}/login", json={
        "username": "admin",
        "password": "admin123",
        "code": "1234",
        "uuid": "test"
    })
    
    if admin_response.status_code != 200:
        print("❌ 管理员登录失败")
        return
    
    admin_result = admin_response.json()
    if admin_result.get('code') != 200:
        print(f"❌ 管理员登录失败: {admin_result.get('msg')}")
        return
    
    admin_token = admin_result.get('data', {}).get('access_token')
    
    # 获取管理员的项目列表
    admin_headers = {"Authorization": f"Bearer {admin_token}"}
    admin_projects_response = requests.get(f"{base_url}/business/projects/list", headers=admin_headers)
    
    if admin_projects_response.status_code == 200:
        admin_projects_result = admin_projects_response.json()
        if admin_projects_result.get('code') == 200:
            admin_projects = admin_projects_result.get('data', {}).get('rows', [])
            print(f"✅ 管理员可以看到 {len(admin_projects)} 个项目")
            
            # 显示项目详情
            for i, project in enumerate(admin_projects[:3]):  # 只显示前3个
                print(f"  项目{i+1}: {project.get('project_name')} (ID: {project.get('project_id')}, 所有者: {project.get('owner_id')})")
        else:
            print(f"❌ 管理员获取项目列表失败: {admin_projects_result.get('msg')}")
    else:
        print(f"❌ 管理员项目列表请求失败: {admin_projects_response.status_code}")
    
    # 2. 检查用户角色
    print(f"\n🔍 检查管理员用户信息...")
    admin_info_response = requests.get(f"{base_url}/getInfo", headers=admin_headers)
    if admin_info_response.status_code == 200:
        admin_info_result = admin_info_response.json()
        if admin_info_result.get('code') == 200:
            user_info = admin_info_result.get('data', {})
            user = user_info.get('user', {})
            roles = user_info.get('roles', [])
            permissions = user_info.get('permissions', [])
            
            print(f"✅ 用户: {user.get('userName')} (ID: {user.get('userId')})")
            print(f"✅ 角色: {roles}")
            print(f"✅ 权限数量: {len(permissions)}")
            
            # 检查是否有超级管理员权限
            has_all_permission = '*:*:*' in permissions
            print(f"✅ 是否有超级权限: {has_all_permission}")
            
            if has_all_permission:
                print("⚠️ 检测到超级管理员权限，这就是为什么能看到所有项目的原因")
                print("💡 如果要测试权限过滤，需要创建一个普通项目成员用户")

def create_test_user():
    """创建测试用户"""
    print(f"\n🔍 创建测试用户...")
    
    # 这里需要管理员权限来创建用户
    # 实际实现需要调用用户管理API
    print("💡 需要通过管理界面创建一个测试用户，然后分配项目成员角色")

def check_database_permissions():
    """检查数据库中的权限配置"""
    print(f"\n🔍 建议检查数据库权限配置...")
    
    sql_commands = [
        "-- 检查用户角色分配",
        "SELECT u.user_name, r.role_name, r.role_key FROM sys_user u",
        "JOIN sys_user_role ur ON u.user_id = ur.user_id", 
        "JOIN sys_role r ON ur.role_id = r.role_id",
        "WHERE u.status = '0' AND r.status = '0';",
        "",
        "-- 检查项目成员分配",
        "SELECT pm.project_id, pm.user_id, pm.role_type, p.project_name FROM rd_project_members pm",
        "JOIN rd_projects p ON pm.project_id = p.project_id",
        "WHERE pm.is_deleted = 0 AND p.is_deleted = 0;",
        "",
        "-- 检查项目所有者",
        "SELECT project_id, project_name, owner_id FROM rd_projects WHERE is_deleted = 0;"
    ]
    
    print("执行以下SQL来检查权限配置:")
    for cmd in sql_commands:
        print(cmd)

def main():
    """主函数"""
    print("🚀 开始权限过滤测试...")
    
    test_admin_vs_member()
    create_test_user()
    check_database_permissions()
    
    print(f"\n📝 权限过滤问题分析:")
    print("1. 如果当前用户是超级管理员(有*:*:*权限)，会绕过权限过滤看到所有项目")
    print("2. 要测试权限过滤效果，需要:")
    print("   - 创建一个普通用户")
    print("   - 分配项目成员角色")
    print("   - 将用户添加到特定项目")
    print("   - 用该用户登录测试")
    print("3. 管理员看到所有项目是正常的，项目成员应该只看到参与的项目")

if __name__ == "__main__":
    main()
