#!/usr/bin/env python3 """ 检查和修复菜单权限问题 """ import sys import os import asyncio from sqlalchemy import text # 添加项目路径到Python路径 sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-platform-backend')) from config.get_db import get_db from module_admin.service.permission_service import PermissionService from config.role_constants import RoleConstants, PermissionConstants async def check_roles(): """检查角色情况""" print("🔍 检查角色情况...") async for db in get_db(): try: # 检查角色 role_query = text(""" SELECT role_id, role_name, role_key, status, del_flag FROM sys_role WHERE del_flag = '0' ORDER BY role_sort """) result = await db.execute(role_query) roles = result.fetchall() print("当前系统角色:") for role in roles: print(f" - ID: {role.role_id}, 名称: {role.role_name}, Key: {role.role_key}, 状态: {role.status}") return roles except Exception as e: print(f"❌ 检查角色失败: {e}") return [] async def check_menus(): """检查菜单情况""" print("\n🔍 检查菜单情况...") async for db in get_db(): try: # 检查项目相关菜单 menu_query = text(""" SELECT menu_id, menu_name, parent_id, path, component, perms, visible, status FROM sys_menu WHERE (menu_name LIKE '%项目%' OR path LIKE '%project%' OR perms LIKE '%project%' OR perms LIKE '%menu:project%') AND status = '0' ORDER BY parent_id, order_num """) result = await db.execute(menu_query) menus = result.fetchall() print("项目相关菜单:") for menu in menus: print(f" - ID: {menu.menu_id}, 名称: {menu.menu_name}, 路径: {menu.path}, 权限: {menu.perms}") return menus except Exception as e: print(f"❌ 检查菜单失败: {e}") return [] async def check_role_menu_permissions(): """检查角色菜单权限""" print("\n🔍 检查项目成员角色权限...") async for db in get_db(): try: # 检查项目成员角色权限 permission_query = text(""" SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.path, m.perms FROM sys_role_menu rm JOIN sys_role r ON rm.role_id = r.role_id JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE r.role_key = 'project_member' AND r.del_flag = '0' AND m.status = '0' ORDER BY m.parent_id, m.order_num """) result = await db.execute(permission_query) permissions = result.fetchall() print("项目成员角色当前权限:") if permissions: for perm in permissions: print(f" - 菜单: {perm.menu_name}, 路径: {perm.path}, 权限: {perm.perms}") else: print(" ❌ 项目成员角色没有任何菜单权限!") return permissions except Exception as e: print(f"❌ 检查角色权限失败: {e}") return [] async def check_user_roles(): """检查用户角色分配""" print("\n🔍 检查用户角色分配...") async for db in get_db(): try: # 检查用户角色 user_role_query = text(""" SELECT ur.user_id, u.user_name, ur.role_id, r.role_name, r.role_key FROM sys_user_role ur JOIN sys_user u ON ur.user_id = u.user_id JOIN sys_role r ON ur.role_id = r.role_id WHERE r.role_key IN ('project_member', 'project_manager', 'admin') AND u.del_flag = '0' AND r.del_flag = '0' ORDER BY ur.user_id, r.role_sort """) result = await db.execute(user_role_query) user_roles = result.fetchall() print("用户角色分配:") for ur in user_roles: print(f" - 用户: {ur.user_name} (ID: {ur.user_id}), 角色: {ur.role_name} ({ur.role_key})") return user_roles except Exception as e: print(f"❌ 检查用户角色失败: {e}") return [] async def fix_menu_permissions(): """修复菜单权限""" print("\n🔧 修复菜单权限...") async for db in get_db(): try: # 1. 确保项目成员角色存在 role_check = text(""" SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' """) result = await db.execute(role_check) role = result.fetchone() if not role: print("创建项目成员角色...") create_role = text(""" INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES ('项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色') """) await db.execute(create_role) await db.commit() print("✅ 项目成员角色创建成功") # 2. 获取角色ID result = await db.execute(role_check) role = result.fetchone() role_id = role.role_id # 3. 确保项目管理菜单存在 menu_check = text(""" SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 """) result = await db.execute(menu_check) menu = result.fetchone() if not menu: print("创建项目管理菜单...") create_menu = text(""" INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES ('项目管理', 0, 2, 'projects', 'projects/index', '', 'Projects', 1, 0, 'C', '0', '0', 'menu:project', 'project', 'admin', NOW(), '项目管理菜单') """) await db.execute(create_menu) print("✅ 项目管理菜单创建成功") # 4. 删除现有权限并重新分配 print("重新分配项目成员角色权限...") delete_perms = text(""" DELETE FROM sys_role_menu WHERE role_id = :role_id """) await db.execute(delete_perms, {"role_id": role_id}) # 5. 分配新权限 permissions_to_assign = [ 'menu:project', # 项目管理菜单 'business:projects:list', # 项目列表权限 'menu:tools', # 工具菜单 'menu:home' # 首页菜单 ] for perm in permissions_to_assign: assign_perm = text(""" INSERT INTO sys_role_menu (role_id, menu_id) SELECT :role_id, menu_id FROM sys_menu WHERE perms = :perm AND status = '0' """) await db.execute(assign_perm, {"role_id": role_id, "perm": perm}) await db.commit() print("✅ 菜单权限修复完成") return True except Exception as e: await db.rollback() print(f"❌ 修复菜单权限失败: {e}") return False async def main(): """主函数""" print("🚀 开始检查菜单权限问题...") # 1. 检查角色 roles = await check_roles() # 2. 检查菜单 menus = await check_menus() # 3. 检查角色权限 permissions = await check_role_menu_permissions() # 4. 检查用户角色 user_roles = await check_user_roles() # 5. 如果项目成员角色没有权限，进行修复 if not permissions: print("\n❌ 发现问题：项目成员角色没有菜单权限") if await fix_menu_permissions(): print("\n🔄 重新检查修复结果...") await check_role_menu_permissions() else: print("\n❌ 修复失败") return print("\n🎉 检查完成！") print("\n💡 如果前端仍然看不到项目管理菜单，请检查:") print("1. 用户是否已分配项目成员角色") print("2. 前端是否需要重新登录以刷新权限") print("3. 浏览器缓存是否需要清理") if __name__ == "__main__": asyncio.run(main()) 