#!/usr/bin/env python3
"""
测试特定问题的修复效果
1. 菜单结构简化（只要项目管理一个层级）
2. 创建新项目按钮权限控制（只有项目管理员可见）
3. 项目成员权限过滤（只能看到分配的项目）
"""

import requests
import json
import time

class SpecificIssuesTester:
    def __init__(self, base_url="http://localhost:8000/dev-api"):
        self.base_url = base_url
    
    def login(self, username, password):
        """登录获取token"""
        response = requests.post(f"{self.base_url}/login", json={
            "username": username,
            "password": password,
            "code": "1234",
            "uuid": "test"
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}).get('access_token')
        return None
    
    def test_menu_structure(self, token, user_type):
        """测试菜单结构 - 应该只有一个项目管理层级"""
        print(f"\n🔍 测试{user_type}菜单结构...")
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{self.base_url}/getRouters", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                routes = result.get('data', [])
                project_routes = [r for r in routes if r.get('path') == '/projects']
                
                if len(project_routes) == 1:
                    project_route = project_routes[0]
                    children = project_route.get('children', [])
                    component = project_route.get('component')
                    
                    print(f"✅ 找到项目管理路由")
                    print(f"   路径: {project_route.get('path')}")
                    print(f"   组件: {component}")
                    print(f"   子路由数: {len(children)}")
                    
                    # 检查是否是直接指向组件（简化结构）
                    if component and component != 'Layout':
                        print("✅ 菜单结构已简化：直接指向项目页面组件")
                        return True
                    elif len(children) == 1 and children[0].get('path') == 'board':
                        print("✅ 菜单结构正常：包含项目看板子路由")
                        return True
                    else:
                        print("⚠️ 菜单结构可能需要调整")
                        return False
                elif len(project_routes) == 0:
                    print("❌ 没有找到项目管理路由")
                    return False
                else:
                    print(f"❌ 仍有重复路由: {len(project_routes)} 个")
                    return False
        
        print("❌ 获取路由失败")
        return False
    
    def test_user_permissions(self, token, user_type):
        """测试用户权限"""
        print(f"\n🔍 测试{user_type}权限...")
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{self.base_url}/getInfo", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                user_info = result.get('data', {})
                user = user_info.get('user', {})
                roles = user_info.get('roles', [])
                permissions = user_info.get('permissions', [])
                
                print(f"✅ 用户: {user.get('userName')} (ID: {user.get('userId')})")
                print(f"✅ 角色: {roles}")
                
                # 检查是否有创建项目权限
                has_create_permission = (
                    'admin' in roles or 
                    'project_manager' in roles or
                    'business:projects:add' in permissions or
                    '*:*:*' in permissions
                )
                
                print(f"✅ 创建项目权限: {has_create_permission}")
                
                # 检查项目相关权限
                project_perms = [p for p in permissions if 'project' in p.lower()]
                print(f"✅ 项目相关权限数: {len(project_perms)}")
                
                return {
                    'roles': roles,
                    'has_create_permission': has_create_permission,
                    'project_permissions': project_perms
                }
        
        print("❌ 获取用户信息失败")
        return None
    
    def test_project_list_filtering(self, token, user_type, expected_limited=False):
        """测试项目列表权限过滤"""
        print(f"\n🔍 测试{user_type}项目列表权限过滤...")
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{self.base_url}/business/projects/list", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                projects = result.get('data', {}).get('rows', [])
                total = result.get('data', {}).get('total', 0)
                
                print(f"✅ 可见项目数: {len(projects)}")
                print(f"✅ 总项目数: {total}")
                
                # 显示项目信息
                for i, project in enumerate(projects[:3]):
                    name = project.get('project_name') or project.get('projectName')
                    owner = project.get('owner_id') or project.get('ownerId')
                    project_id = project.get('project_id') or project.get('projectId')
                    print(f"  项目{i+1}: {name} (ID: {project_id}, 所有者: {owner})")
                
                # 权限过滤验证
                if expected_limited and len(projects) > 0:
                    print("✅ 项目成员可以看到分配的项目")
                    return True, len(projects)
                elif not expected_limited:
                    print("✅ 管理员/项目管理员可以看到所有项目")
                    return True, len(projects)
                else:
                    print("❌ 项目成员看不到任何项目（可能没有分配项目）")
                    return False, 0
            else:
                print(f"❌ 获取项目列表失败: {result.get('msg')}")
        else:
            print(f"❌ 项目列表请求失败: {response.status_code}")
        
        return False, 0
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始特定问题修复验证...")
        
        results = {}
        
        # 测试管理员
        print("\n" + "="*60)
        print("测试管理员用户")
        print("="*60)
        
        admin_token = self.login("admin", "admin123")
        if admin_token:
            print("✅ 管理员登录成功")
            
            # 测试菜单结构
            menu_ok = self.test_menu_structure(admin_token, "管理员")
            results['admin_menu'] = menu_ok
            
            # 测试权限
            admin_perms = self.test_user_permissions(admin_token, "管理员")
            results['admin_permissions'] = admin_perms
            
            # 测试项目列表
            list_ok, admin_project_count = self.test_project_list_filtering(admin_token, "管理员", expected_limited=False)
            results['admin_project_list'] = list_ok
            results['admin_project_count'] = admin_project_count
        else:
            print("❌ 管理员登录失败")
            return False
        
        # 测试项目成员
        print("\n" + "="*60)
        print("测试项目成员用户")
        print("="*60)
        
        member_token = self.login("testmember", "admin123")
        if member_token:
            print("✅ 项目成员登录成功")
            
            # 测试菜单结构
            menu_ok = self.test_menu_structure(member_token, "项目成员")
            results['member_menu'] = menu_ok
            
            # 测试权限
            member_perms = self.test_user_permissions(member_token, "项目成员")
            results['member_permissions'] = member_perms
            
            # 测试项目列表权限过滤
            list_ok, member_project_count = self.test_project_list_filtering(member_token, "项目成员", expected_limited=True)
            results['member_project_list'] = list_ok
            results['member_project_count'] = member_project_count
            
            # 权限过滤效果验证
            if admin_project_count > member_project_count:
                print("✅ 权限过滤正常：项目成员看到的项目少于管理员")
                results['permission_filtering'] = True
            elif admin_project_count == member_project_count and member_project_count > 0:
                print("❌ 权限过滤失效：项目成员看到了所有项目")
                results['permission_filtering'] = False
            else:
                print("⚠️ 无法判断权限过滤效果（可能没有项目数据）")
                results['permission_filtering'] = None
        else:
            print("⚠️ 项目成员用户不存在或登录失败")
            print("💡 请先执行 create_test_member.sql 创建测试用户")
            results['member_exists'] = False
        
        # 总结
        print("\n" + "="*60)
        print("📊 问题修复验证总结")
        print("="*60)
        
        # 问题1：菜单结构简化
        menu_fixed = results.get('admin_menu', False) and results.get('member_menu', False)
        print(f"{'✅' if menu_fixed else '❌'} 问题1: 菜单结构简化")
        
        # 问题2：创建按钮权限控制
        admin_can_create = results.get('admin_permissions', {}).get('has_create_permission', False)
        member_cannot_create = not results.get('member_permissions', {}).get('has_create_permission', True)
        button_permission_ok = admin_can_create and member_cannot_create
        print(f"{'✅' if button_permission_ok else '❌'} 问题2: 创建按钮权限控制")
        if admin_can_create:
            print("  ✅ 管理员有创建权限")
        else:
            print("  ❌ 管理员没有创建权限")
        if member_cannot_create:
            print("  ✅ 项目成员没有创建权限")
        else:
            print("  ❌ 项目成员有创建权限（不应该有）")
        
        # 问题3：项目权限过滤
        filtering_ok = results.get('permission_filtering', False)
        print(f"{'✅' if filtering_ok else '❌'} 问题3: 项目权限过滤")
        if filtering_ok:
            print(f"  ✅ 管理员看到 {results.get('admin_project_count', 0)} 个项目")
            print(f"  ✅ 项目成员看到 {results.get('member_project_count', 0)} 个项目")
        elif filtering_ok is False:
            print("  ❌ 权限过滤失效")
        else:
            print("  ⚠️ 无法验证权限过滤效果")
        
        # 总体结果
        all_fixed = menu_fixed and button_permission_ok and filtering_ok
        if all_fixed:
            print("\n🎉 所有问题已修复！")
        else:
            print(f"\n⚠️ 还有问题需要解决")
            if not menu_fixed:
                print("  - 菜单结构需要调整")
            if not button_permission_ok:
                print("  - 创建按钮权限控制需要完善")
            if not filtering_ok:
                print("  - 项目权限过滤需要修复")
        
        return all_fixed

def main():
    """主函数"""
    tester = SpecificIssuesTester()
    success = tester.run_comprehensive_test()
    
    print(f"\n📝 修复建议:")
    print("1. 执行 check_menu_duplicate.sql 修复菜单和权限")
    print("2. 执行 create_test_member.sql 创建测试用户")
    print("3. 重启后端服务")
    print("4. 清理浏览器缓存并重新登录")
    
    return success

if __name__ == "__main__":
    main()
