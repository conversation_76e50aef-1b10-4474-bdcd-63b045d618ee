<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .warning {
            color: orange;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>菜单权限修复测试</h1>
    
    <div class="test-section">
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 获取用户信息</h2>
        <button onclick="testUserInfo()">获取用户信息</button>
        <div id="userInfoResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 获取路由信息</h2>
        <button onclick="testRouters()">获取路由</button>
        <div id="routersResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 测试项目API</h2>
        <button onclick="testProjectsAPI()">测试项目列表</button>
        <div id="projectsResult"></div>
    </div>

    <script>
        let token = '';
        const baseURL = 'http://localhost:8000/dev-api';
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '正在登录...';
            
            try {
                const response = await fetch(`${baseURL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123',
                        code: '1234',
                        uuid: 'test'
                    })
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    token = result.data.access_token;
                    resultDiv.innerHTML = `<span class="success">✅ 登录成功</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败: ${result.msg}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 登录异常: ${error.message}</span>`;
            }
        }
        
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            if (!token) {
                resultDiv.innerHTML = '<span class="warning">⚠️ 请先登录</span>';
                return;
            }
            
            resultDiv.innerHTML = '正在获取用户信息...';
            
            try {
                const response = await fetch(`${baseURL}/getInfo`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    const user = result.data.user;
                    const roles = result.data.roles;
                    const permissions = result.data.permissions;
                    
                    const projectPerms = permissions.filter(p => p.toLowerCase().includes('project'));
                    
                    resultDiv.innerHTML = `
                        <span class="success">✅ 用户信息获取成功</span><br>
                        <strong>用户:</strong> ${user.userName} (ID: ${user.userId})<br>
                        <strong>角色:</strong> ${roles.join(', ')}<br>
                        <strong>权限总数:</strong> ${permissions.length}<br>
                        <strong>项目相关权限:</strong> ${projectPerms.length > 0 ? projectPerms.join(', ') : '无'}
                    `;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取用户信息失败: ${result.msg}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 获取用户信息异常: ${error.message}</span>`;
            }
        }
        
        async function testRouters() {
            const resultDiv = document.getElementById('routersResult');
            if (!token) {
                resultDiv.innerHTML = '<span class="warning">⚠️ 请先登录</span>';
                return;
            }
            
            resultDiv.innerHTML = '正在获取路由信息...';
            
            try {
                const response = await fetch(`${baseURL}/getRouters`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    const routers = result.data;
                    const projectRoutes = routers.filter(r => 
                        r.path && r.path.toLowerCase().includes('project')
                    );
                    
                    let routeInfo = `<span class="success">✅ 路由获取成功</span><br>`;
                    routeInfo += `<strong>路由总数:</strong> ${routers.length}<br>`;
                    routeInfo += `<strong>项目相关路由:</strong> ${projectRoutes.length}<br>`;
                    
                    if (projectRoutes.length > 0) {
                        routeInfo += '<strong>项目路由详情:</strong><br>';
                        projectRoutes.forEach(route => {
                            routeInfo += `- ${route.name || route.path}: ${route.path}<br>`;
                            if (route.children && route.children.length > 0) {
                                route.children.forEach(child => {
                                    routeInfo += `  └─ ${child.name || child.path}: ${child.path}<br>`;
                                });
                            }
                        });
                    }
                    
                    resultDiv.innerHTML = routeInfo;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取路由失败: ${result.msg}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 获取路由异常: ${error.message}</span>`;
            }
        }
        
        async function testProjectsAPI() {
            const resultDiv = document.getElementById('projectsResult');
            if (!token) {
                resultDiv.innerHTML = '<span class="warning">⚠️ 请先登录</span>';
                return;
            }
            
            resultDiv.innerHTML = '正在测试项目API...';
            
            try {
                const response = await fetch(`${baseURL}/business/projects/list?pageNum=1&pageSize=10`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    const projects = result.data.rows || [];
                    resultDiv.innerHTML = `
                        <span class="success">✅ 项目API测试成功</span><br>
                        <strong>项目数量:</strong> ${projects.length}<br>
                        <strong>总记录数:</strong> ${result.data.total || 0}
                    `;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 项目API测试失败: ${result.msg}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 项目API测试异常: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
