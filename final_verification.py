#!/usr/bin/env python3
"""
最终验证脚本 - 验证所有问题是否已解决
"""

import requests
import json
import time

def test_login(username, password):
    """登录获取token"""
    response = requests.post("http://localhost:8000/dev-api/login", json={
        "username": username,
        "password": password,
        "code": "1234",
        "uuid": "test"
    })
    
    if response.status_code == 200:
        result = response.json()
        if result.get('code') == 200:
            return result.get('data', {}).get('access_token')
    return None

def test_menu_routes(token, user_type):
    """测试菜单路由"""
    print(f"\n🔍 测试{user_type}菜单路由...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get("http://localhost:8000/dev-api/getRouters", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('code') == 200:
            routes = result.get('data', [])
            project_routes = [r for r in routes if r.get('path') == '/projects']
            
            print(f"总路由数: {len(routes)}")
            print(f"项目管理路由数: {len(project_routes)}")
            
            if len(project_routes) == 1:
                print("✅ 菜单重复问题已解决")
                project_route = project_routes[0]
                children = project_route.get('children', [])
                print(f"子路由数: {len(children)}")
                return True
            elif len(project_routes) == 0:
                print("❌ 没有项目管理路由")
                return False
            else:
                print(f"❌ 仍有重复路由: {len(project_routes)} 个")
                return False
    
    print("❌ 获取路由失败")
    return False

def test_project_list(token, user_type):
    """测试项目列表"""
    print(f"\n🔍 测试{user_type}项目列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get("http://localhost:8000/dev-api/business/projects/list", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('code') == 200:
            projects = result.get('data', {}).get('rows', [])
            print(f"可见项目数: {len(projects)}")
            
            for project in projects:
                name = project.get('project_name') or project.get('projectName')
                owner = project.get('owner_id') or project.get('ownerId')
                print(f"  - {name} (所有者: {owner})")
            
            return True, len(projects)
        else:
            print(f"❌ 获取项目列表失败: {result.get('msg')}")
    else:
        print(f"❌ 项目列表请求失败: {response.status_code}")
    
    return False, 0

def test_project_detail(token, user_type):
    """测试项目详情包含成员信息"""
    print(f"\n🔍 测试{user_type}项目详情...")
    
    # 先获取项目列表
    headers = {"Authorization": f"Bearer {token}"}
    list_response = requests.get("http://localhost:8000/dev-api/business/projects/list", headers=headers)
    
    if list_response.status_code != 200:
        print("❌ 无法获取项目列表")
        return False
    
    list_result = list_response.json()
    if list_result.get('code') != 200:
        print("❌ 获取项目列表失败")
        return False
    
    projects = list_result.get('data', {}).get('rows', [])
    if not projects:
        print("⚠️ 没有项目可以测试")
        return True
    
    # 测试第一个项目的详情
    project = projects[0]
    project_id = project.get('project_id') or project.get('projectId')
    project_name = project.get('project_name') or project.get('projectName')
    
    print(f"测试项目: {project_name} (ID: {project_id})")
    
    detail_response = requests.get(f"http://localhost:8000/dev-api/business/projects/{project_id}", headers=headers)
    
    if detail_response.status_code == 200:
        detail_result = detail_response.json()
        if detail_result.get('code') == 200:
            project_detail = detail_result.get('data', {})
            
            # 检查成员信息
            members = project_detail.get('project_members') or project_detail.get('projectMembers')
            if members is not None:
                print(f"✅ 项目详情包含成员信息，成员数: {len(members)}")
                for member in members[:3]:  # 只显示前3个
                    user_id = member.get('user_id') or member.get('userId')
                    role_type = member.get('role_type') or member.get('roleType')
                    print(f"  - 用户ID: {user_id}, 角色: {role_type}")
                return True
            else:
                print("❌ 项目详情不包含成员信息")
                return False
        else:
            print(f"❌ 获取项目详情失败: {detail_result.get('msg')}")
    else:
        print(f"❌ 项目详情请求失败: {detail_response.status_code}")
    
    return False

def main():
    """主函数"""
    print("🚀 开始最终验证...")
    
    # 测试管理员
    print("\n" + "="*60)
    print("测试管理员用户")
    print("="*60)
    
    admin_token = test_login("admin", "admin123")
    if not admin_token:
        print("❌ 管理员登录失败")
        return False
    
    admin_results = []
    admin_results.append(("菜单路由", test_menu_routes(admin_token, "管理员")))
    admin_results.append(("项目详情成员信息", test_project_detail(admin_token, "管理员")))
    
    success, admin_project_count = test_project_list(admin_token, "管理员")
    admin_results.append(("项目列表", success))
    
    # 测试项目成员（如果存在）
    print("\n" + "="*60)
    print("测试项目成员用户")
    print("="*60)
    
    member_token = test_login("testmember", "admin123")
    member_results = []
    
    if member_token:
        print("✅ 测试成员登录成功")
        member_results.append(("菜单路由", test_menu_routes(member_token, "项目成员")))
        
        success, member_project_count = test_project_list(member_token, "项目成员")
        member_results.append(("项目列表", success))
        
        member_results.append(("项目详情成员信息", test_project_detail(member_token, "项目成员")))
        
        # 权限过滤验证
        if success and admin_project_count > member_project_count:
            print("✅ 权限过滤正常工作：项目成员看到的项目少于管理员")
            member_results.append(("权限过滤", True))
        elif success and admin_project_count == member_project_count:
            print("⚠️ 权限过滤可能有问题：项目成员看到了所有项目")
            member_results.append(("权限过滤", False))
        else:
            member_results.append(("权限过滤", False))
    else:
        print("⚠️ 测试成员用户不存在或登录失败")
        print("💡 请先执行 create_test_member.sql 创建测试用户")
    
    # 总结
    print("\n" + "="*60)
    print("📊 验证总结")
    print("="*60)
    
    print("\n管理员测试结果:")
    admin_passed = 0
    for test_name, result in admin_results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            admin_passed += 1
    
    if member_results:
        print("\n项目成员测试结果:")
        member_passed = 0
        for test_name, result in member_results:
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")
            if result:
                member_passed += 1
        
        total_passed = admin_passed + member_passed
        total_tests = len(admin_results) + len(member_results)
    else:
        total_passed = admin_passed
        total_tests = len(admin_results)
    
    print(f"\n总体通过率: {total_passed}/{total_tests}")
    
    # 问题解决状态
    print(f"\n📝 问题解决状态:")
    
    menu_fixed = any(result for name, result in admin_results if "菜单" in name)
    print(f"{'✅' if menu_fixed else '❌'} 问题1: 菜单重复问题")
    
    if member_results:
        permission_fixed = any(result for name, result in member_results if "权限过滤" in name)
        print(f"{'✅' if permission_fixed else '❌'} 问题2: 权限过滤问题")
    else:
        print("⚠️ 问题2: 权限过滤问题 - 需要创建测试用户验证")
    
    detail_fixed = any(result for name, result in admin_results if "成员信息" in name)
    print(f"{'✅' if detail_fixed else '❌'} 问题3: 项目详情成员信息问题")
    
    if total_passed == total_tests:
        print("\n🎉 所有问题已解决！")
        return True
    else:
        print(f"\n⚠️ 还有 {total_tests - total_passed} 个问题需要解决")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
