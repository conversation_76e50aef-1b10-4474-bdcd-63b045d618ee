from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query


class ProjectMembersModel(BaseModel):
    """
    项目成员表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    project_id: Optional[int] = Field(default=None, description='项目ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    role_type: Optional[str] = Field(default='member', description='角色类型：owner-所有者，manager-管理员，member-成员')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')
    updated_at: Optional[datetime] = Field(default=None, description='更新时间')
    is_deleted: Optional[bool] = Field(default=False, description='是否删除')

    def validate_fields(self):
        pass


class AddProjectMemberModel(BaseModel):
    """
    添加项目成员模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: int = Field(description='项目ID')
    user_id: int = Field(description='用户ID')
    role_type: str = Field(default='member', description='角色类型：owner-所有者，manager-管理员，member-成员')
    create_by: Optional[str] = Field(default=None, description='创建者')

    def validate_fields(self):
        pass


class ProjectMembersQueryModel(ProjectMembersModel):
    """
    项目成员不分页查询模型
    """
    pass


@as_query
class ProjectMembersPageQueryModel(ProjectMembersQueryModel):
    """
    项目成员分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    order_by_column: Optional[str] = Field(default=None, description='排序字段')
    is_asc: Optional[bool] = Field(default=True, description='是否升序')


class DeleteProjectMembersModel(BaseModel):
    """
    删除项目成员模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    member_ids: str = Field(description='需要删除的成员ID')
