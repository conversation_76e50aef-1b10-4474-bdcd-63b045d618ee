-- 综合修复脚本：解决菜单重复、权限控制等问题
-- 执行前请备份数据库

-- 1. 清理重复和错误的菜单配置
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE (path = 'projects' OR menu_name LIKE '%项目%') AND parent_id = 0
);
DELETE FROM sys_menu WHERE (path = 'projects' OR menu_name LIKE '%项目%');

-- 2. 重新创建项目管理菜单结构
-- 主菜单（目录类型）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目管理', 0, 2, 'projects', 'Layout', '', 'Projects', 1, 0, 'M', '0', '0', 'menu:project', 'project', 'admin', NOW(), '项目管理主菜单');

-- 获取主菜单ID
SET @project_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1);

-- 项目看板子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目看板', @project_menu_id, 1, 'board', 'projects/board', '', 'ProjectsBoard', 1, 0, 'C', '0', '0', 'business:projects:list', 'kanban', 'admin', NOW(), '项目看板菜单');

-- 项目详情子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目详情', @project_menu_id, 2, 'detail/:id', 'projects/detail', '', 'ProjectDetail', 1, 0, 'C', '1', '0', 'business:projects:view', 'kanban', 'admin', NOW(), '项目详情菜单');

-- 项目成员管理子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('项目成员管理', @project_menu_id, 3, 'members/:id', 'projects/members', '', 'projectMembers', 1, 0, 'C', '1', '0', 'business:projects:member:manage', 'user', 'admin', NOW(), '项目成员管理菜单');

-- 创建任务子菜单（隐藏）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES ('创建任务', @project_menu_id, 4, 'create-task/:projectId', 'projects/create-task', '', 'CreateTask', 1, 0, 'C', '1', '0', 'business:tasks:add', 'kanban', 'admin', NOW(), '创建任务菜单');

-- 3. 确保项目成员角色存在
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) 
SELECT '项目成员', 'project_member', 4, '5', 1, 1, '0', '0', 'admin', NOW(), '项目成员角色'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0');

-- 获取角色ID
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member' AND del_flag = '0' LIMIT 1);
SET @admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'admin' AND del_flag = '0' LIMIT 1);

-- 4. 为项目成员角色分配菜单权限
-- 清理现有权限
DELETE FROM sys_role_menu WHERE role_id = @project_member_role_id;

-- 分配基础权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_member_role_id, menu_id FROM sys_menu 
WHERE perms IN (
    'menu:project',              -- 项目管理主菜单
    'business:projects:list',    -- 项目列表权限
    'business:projects:view',    -- 项目查看权限
    'menu:tools',               -- 工具菜单
    'menu:home'                 -- 首页菜单
) AND status = '0' AND @project_member_role_id IS NOT NULL;

-- 分配项目相关菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @project_member_role_id, menu_id FROM sys_menu 
WHERE (path = 'projects' OR path = 'board' OR parent_id = @project_menu_id) 
AND status = '0' AND @project_member_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 5. 为管理员角色分配项目管理权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE (path = 'projects' OR parent_id = @project_menu_id) 
AND status = '0' AND @admin_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 6. 创建项目权限相关的权限标识
-- 确保权限菜单存在
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '项目编辑', @project_menu_id, 10, '', '', '', '', 1, 0, 'F', '1', '0', 'business:projects:edit', '', 'admin', NOW(), '项目编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:edit');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '项目删除', @project_menu_id, 11, '', '', '', '', 1, 0, 'F', '1', '0', 'business:projects:remove', '', 'admin', NOW(), '项目删除权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:projects:remove');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '任务编辑', @project_menu_id, 12, '', '', '', '', 1, 0, 'F', '1', '0', 'business:tasks:edit', '', 'admin', NOW(), '任务编辑权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'business:tasks:edit');

-- 7. 只为管理员分配编辑和删除权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE perms IN (
    'business:projects:edit',
    'business:projects:remove',
    'business:tasks:edit'
) AND status = '0' AND @admin_role_id IS NOT NULL
ON DUPLICATE KEY UPDATE role_id = VALUES(role_id);

-- 8. 检查结果
SELECT '=== 菜单结构检查 ===' as info;
SELECT m.menu_id, m.menu_name, m.parent_id, m.path, m.component, m.route_name, m.perms, m.visible, m.status
FROM sys_menu m
WHERE (m.path = 'projects' AND m.parent_id = 0) 
   OR m.parent_id = (SELECT menu_id FROM sys_menu WHERE path = 'projects' AND parent_id = 0 LIMIT 1)
ORDER BY m.parent_id, m.order_num;

SELECT '=== 角色权限检查 ===' as info;
SELECT r.role_name, COUNT(rm.menu_id) as menu_count
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key IN ('admin', 'project_member') AND r.del_flag = '0'
GROUP BY r.role_id, r.role_name;

-- 提交更改
COMMIT;
